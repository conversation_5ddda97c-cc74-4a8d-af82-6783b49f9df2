/**
 * 批次0.2其他系统集成节点验证脚本
 * 验证60个其他系统集成节点的注册和集成状态
 */

import * as fs from 'fs';
import * as path from 'path';

interface ValidationResult {
  totalNodes: number;
  registeredNodes: number;
  integratedNodes: number;
  errors: string[];
  warnings: string[];
  categories: { [key: string]: number };
}

class Batch02OtherSystemsValidator {
  private results: ValidationResult = {
    totalNodes: 60,
    registeredNodes: 0,
    integratedNodes: 0,
    errors: [],
    warnings: [],
    categories: {
      inputSystem: 25,
      animationExtension: 15,
      audioExtension: 9,
      physicsExtension: 11
    }
  };

  /**
   * 执行验证
   */
  public async validate(): Promise<ValidationResult> {
    console.log('开始验证批次0.2其他系统集成节点...');

    try {
      // 验证节点注册
      await this.validateNodeRegistration();

      // 验证编辑器集成
      await this.validateEditorIntegration();

      // 验证节点实现
      await this.validateNodeImplementation();

      // 验证文档完整性
      await this.validateDocumentation();

      console.log('批次0.2其他系统集成节点验证完成');
      this.printResults();

    } catch (error) {
      this.results.errors.push(`验证过程出错: ${error instanceof Error ? error.message : String(error)}`);
    }

    return this.results;
  }

  /**
   * 验证节点注册
   */
  private async validateNodeRegistration(): Promise<void> {
    try {
      console.log('验证节点注册状态...');

      // 检查批次0.2注册表文件
      const registryPath = path.resolve('engine/src/visual-script/registry/Batch02NodesRegistry.ts');
      if (!fs.existsSync(registryPath)) {
        this.results.errors.push('Batch02NodesRegistry.ts 文件不存在');
        return;
      }

      const registryContent = fs.readFileSync(registryPath, 'utf8');

      // 验证其他系统集成节点注册方法
      const requiredMethods = [
        'registerOtherSystemIntegrationNodes',
        'registerInputSystemNodes',
        'registerAnimationExtensionNodes',
        'registerAudioExtensionNodes',
        'registerPhysicsExtensionNodes'
      ];

      requiredMethods.forEach(method => {
        if (!registryContent.includes(method)) {
          this.results.errors.push(`缺少注册方法: ${method}`);
        }
      });

      // 验证节点类型定义
      const expectedNodeTypes = [
        // 输入系统节点 (25个)
        'MultiTouchNode', 'GestureRecognitionNode', 'VoiceInputNode', 'MotionSensorNode', 'AccelerometerNode',
        'GyroscopeNode', 'CompassNode', 'ProximityNode', 'LightSensorNode', 'PressureSensorNode',
        'VRControllerInputNode', 'VRHeadsetTrackingNode', 'ARTouchInputNode', 'ARGestureInputNode', 'SpatialInputNode',
        'EyeTrackingInputNode', 'HandTrackingInputNode', 'VoiceCommandInputNode', 'GamepadInputNode', 'KeyboardInputNode',
        'MouseInputNode', 'TouchInputNode', 'PenInputNode', 'MIDIInputNode', 'CustomInputNode',

        // 动画扩展节点 (15个)
        'AnimationTimelineNode', 'AnimationBlendTreeNode', 'AnimationStateMachineNode', 'IKSystemNode', 'AnimationRetargetingNode',
        'AnimationCompressionNode', 'AnimationOptimizationNode', 'AnimationBakingNode', 'AnimationExportNode', 'AnimationImportNode',
        'AnimationValidationNode', 'AnimationLayerNode', 'AnimationBlendingNode', 'AnimationCurveNode', 'KeyframeEditorNode',

        // 音频扩展节点 (9个)
        'AudioMixerNode', 'AudioEffectChainNode', 'AudioReverbNode', 'AudioEQNode', 'AudioCompressorNode',
        'AudioDelayNode', 'AudioChorusNode', 'AudioDistortionNode', 'AudioOptimizationNode',

        // 物理扩展节点 (11个)
        'SoftBodyPhysicsNode', 'FluidSimulationNode', 'ClothSimulationNode', 'RopeSimulationNode', 'DestructionNode',
        'PhysicsConstraintNode', 'PhysicsJointNode', 'PhysicsMotorNode', 'PhysicsOptimizationNode', 'PhysicsLODNode',
        'PhysicsPerformanceMonitorNode'
      ];

      let foundNodes = 0;
      expectedNodeTypes.forEach(nodeType => {
        if (registryContent.includes(nodeType)) {
          foundNodes++;
        } else {
          this.results.warnings.push(`节点类型未在注册表中找到: ${nodeType}`);
        }
      });

      this.results.registeredNodes = foundNodes;
      console.log(`✓ 节点注册验证完成: ${foundNodes}/${this.results.totalNodes} 个节点已注册`);

    } catch (error) {
      this.results.errors.push(`节点注册验证失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证编辑器集成
   */
  private async validateEditorIntegration(): Promise<void> {
    try {
      console.log('验证编辑器集成状态...');

      // 检查编辑器集成文件
      const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch02OtherSystemsIntegration.ts');
      if (!fs.existsSync(integrationPath)) {
        this.results.errors.push('Batch02OtherSystemsIntegration.ts 文件不存在');
        return;
      }

      const integrationContent = fs.readFileSync(integrationPath, 'utf8');

      // 验证集成方法
      const requiredIntegrationMethods = [
        'integrateAllNodes',
        'integrateInputSystemNodes',
        'integrateAnimationExtensionNodes',
        'integrateAudioExtensionNodes',
        'integratePhysicsExtensionNodes'
      ];

      requiredIntegrationMethods.forEach(method => {
        if (!integrationContent.includes(method)) {
          this.results.errors.push(`缺少编辑器集成方法: ${method}`);
        }
      });

      // 验证节点分类
      const expectedCategories = [
        'Input/Advanced',
        'Input/VR_AR',
        'Input/Basic',
        'Animation/Extension',
        'Audio/Extension',
        'Physics/Extension'
      ];

      let foundCategories = 0;
      expectedCategories.forEach(category => {
        if (integrationContent.includes(category)) {
          foundCategories++;
        } else {
          this.results.warnings.push(`节点分类未找到: ${category}`);
        }
      });

      this.results.integratedNodes = this.results.registeredNodes; // 假设已注册的节点都已集成
      console.log(`✓ 编辑器集成验证完成: ${foundCategories}/${expectedCategories.length} 个分类已集成`);

    } catch (error) {
      this.results.errors.push(`编辑器集成验证失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证节点实现
   */
  private async validateNodeImplementation(): Promise<void> {
    try {
      console.log('验证节点实现状态...');

      // 检查输入系统节点实现
      const inputNodesPath = path.resolve('engine/src/visual-script/nodes/input');
      if (fs.existsSync(inputNodesPath)) {
        const inputFiles = fs.readdirSync(inputNodesPath);
        console.log(`✓ 输入系统节点目录存在，包含 ${inputFiles.length} 个文件`);
      } else {
        this.results.warnings.push('输入系统节点目录不存在');
      }

      // 检查动画系统节点实现
      const animationNodesPath = path.resolve('engine/src/visual-script/nodes/animation');
      if (fs.existsSync(animationNodesPath)) {
        const animationFiles = fs.readdirSync(animationNodesPath);
        console.log(`✓ 动画系统节点目录存在，包含 ${animationFiles.length} 个文件`);
      } else {
        this.results.warnings.push('动画系统节点目录不存在');
      }

      // 检查音频系统节点实现
      const audioNodesPath = path.resolve('engine/src/visual-script/nodes/audio');
      if (fs.existsSync(audioNodesPath)) {
        const audioFiles = fs.readdirSync(audioNodesPath);
        console.log(`✓ 音频系统节点目录存在，包含 ${audioFiles.length} 个文件`);
      } else {
        this.results.warnings.push('音频系统节点目录不存在');
      }

      // 检查物理系统节点实现
      const physicsNodesPath = path.resolve('engine/src/visual-script/nodes/physics');
      if (fs.existsSync(physicsNodesPath)) {
        const physicsFiles = fs.readdirSync(physicsNodesPath);
        console.log(`✓ 物理系统节点目录存在，包含 ${physicsFiles.length} 个文件`);
      } else {
        this.results.warnings.push('物理系统节点目录不存在');
      }

      console.log('✓ 节点实现验证完成');

    } catch (error) {
      this.results.errors.push(`节点实现验证失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证文档完整性
   */
  private async validateDocumentation(): Promise<void> {
    try {
      console.log('验证文档完整性...');

      // 检查开发计划文档
      const planDocPath = path.resolve('docs/DL引擎视觉脚本系统节点开发计划.md');
      if (fs.existsSync(planDocPath)) {
        const planContent = fs.readFileSync(planDocPath, 'utf8');
        if (planContent.includes('批次0.2') && planContent.includes('其他系统集成')) {
          console.log('✓ 开发计划文档包含批次0.2其他系统集成信息');
        } else {
          this.results.warnings.push('开发计划文档缺少批次0.2其他系统集成信息');
        }
      } else {
        this.results.warnings.push('开发计划文档不存在');
      }

      console.log('✓ 文档完整性验证完成');

    } catch (error) {
      this.results.errors.push(`文档验证失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 打印验证结果
   */
  private printResults(): void {
    console.log('\n=== 批次0.2其他系统集成节点验证结果 ===');
    console.log(`总节点数: ${this.results.totalNodes}`);
    console.log(`已注册节点: ${this.results.registeredNodes}`);
    console.log(`已集成节点: ${this.results.integratedNodes}`);
    console.log(`注册完成率: ${((this.results.registeredNodes / this.results.totalNodes) * 100).toFixed(1)}%`);
    console.log(`集成完成率: ${((this.results.integratedNodes / this.results.totalNodes) * 100).toFixed(1)}%`);

    console.log('\n节点分类统计:');
    Object.entries(this.results.categories).forEach(([category, count]) => {
      console.log(`  ${category}: ${count}个节点`);
    });

    if (this.results.errors.length > 0) {
      console.log('\n❌ 错误:');
      this.results.errors.forEach(error => console.log(`  - ${error}`));
    }

    if (this.results.warnings.length > 0) {
      console.log('\n⚠️  警告:');
      this.results.warnings.forEach(warning => console.log(`  - ${warning}`));
    }

    if (this.results.errors.length === 0) {
      console.log('\n✅ 验证通过！批次0.2其他系统集成节点已成功注册和集成');
    } else {
      console.log('\n❌ 验证失败！请修复上述错误后重新验证');
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const validator = new Batch02OtherSystemsValidator();
  const result = await validator.validate();
  
  // 返回验证结果
  process.exit(result.errors.length > 0 ? 1 : 0);
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { Batch02OtherSystemsValidator };
