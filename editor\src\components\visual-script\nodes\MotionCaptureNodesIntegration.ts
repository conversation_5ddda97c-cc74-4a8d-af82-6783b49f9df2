/**
 * 动作捕捉节点编辑器集成
 * 为动作捕捉节点提供专用的编辑器界面和可视化组件
 */

import { NodeEditor } from '../NodeEditor';
import { NodeInfo } from '../../../types/NodeTypes';

export class MotionCaptureNodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Set<string> = new Set();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
  }

  /**
   * 集成所有动作捕捉节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成动作捕捉节点到编辑器...');

    // 集成摄像头输入节点
    this.integrateCameraInputNode();

    // 集成动作捕捉初始化节点
    this.integrateMotionCaptureInitNode();

    // 集成骨骼追踪节点
    this.integrateSkeletonTrackingNode();

    // 集成面部追踪节点
    this.integrateFaceTrackingNode();

    // 集成手部追踪节点
    this.integrateHandTrackingNode();

    // 集成身体追踪节点
    this.integrateBodyTrackingNode();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('动作捕捉节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成摄像头输入节点
   */
  private integrateCameraInputNode(): void {
    this.registerNode({
      type: 'CameraInput',
      name: '摄像头输入',
      description: '摄像头输入节点，提供视频流数据获取和处理功能',
      category: 'MotionCapture/Input',
      icon: 'videocam',
      color: '#FF9800',
      tags: ['camera', 'input', 'video'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showDeviceSelector: true,
        showSettings: true,
        customPanels: ['deviceSelector', 'cameraSettings', 'videoPreview', 'streamStatus']
      }
    });

    console.log('摄像头输入节点集成完成');
  }

  /**
   * 集成动作捕捉初始化节点
   */
  private integrateMotionCaptureInitNode(): void {
    this.registerNode({
      type: 'MotionCaptureInit',
      name: '动作捕捉初始化',
      description: '动作捕捉系统初始化节点，配置和启动动作捕捉功能',
      category: 'MotionCapture/System',
      icon: 'settings',
      color: '#FF9800',
      tags: ['mocap', 'init', 'system'],
      nodeClass: null,
      editorConfig: {
        showSystemStatus: true,
        showConfiguration: true,
        showCapabilities: true,
        customPanels: ['systemConfig', 'capabilitySettings', 'systemStatus', 'performanceMonitor']
      }
    });

    console.log('动作捕捉初始化节点集成完成');
  }

  /**
   * 集成骨骼追踪节点
   */
  private integrateSkeletonTrackingNode(): void {
    this.registerNode({
      type: 'SkeletonTracking',
      name: '骨骼追踪',
      description: '人体骨骼追踪节点，检测和追踪人体关键点',
      category: 'MotionCapture/Tracking',
      icon: 'accessibility',
      color: '#FF9800',
      tags: ['skeleton', 'tracking', 'pose'],
      nodeClass: null,
      editorConfig: {
        showSkeletonVisualization: true,
        showTrackingSettings: true,
        showQualityMetrics: true,
        customPanels: ['skeletonViewer', 'trackingSettings', 'keypointList', 'qualityMonitor']
      }
    });

    console.log('骨骼追踪节点集成完成');
  }

  /**
   * 集成面部追踪节点
   */
  private integrateFaceTrackingNode(): void {
    this.registerNode({
      type: 'FaceTracking',
      name: '面部追踪',
      description: '面部特征追踪节点，检测面部关键点和表情',
      category: 'MotionCapture/Tracking',
      icon: 'face',
      color: '#FF9800',
      tags: ['face', 'tracking', 'expression'],
      nodeClass: null,
      editorConfig: {
        showFaceVisualization: true,
        showExpressionAnalysis: true,
        showGazeTracking: true,
        customPanels: ['faceViewer', 'landmarkEditor', 'expressionMonitor', 'gazeTracker']
      }
    });

    console.log('面部追踪节点集成完成');
  }

  /**
   * 集成手部追踪节点
   */
  private integrateHandTrackingNode(): void {
    this.registerNode({
      type: 'HandTracking',
      name: '手部追踪',
      description: '手部追踪节点，检测手部关键点和手势识别',
      category: 'MotionCapture/Tracking',
      icon: 'pan_tool',
      color: '#FF9800',
      tags: ['hand', 'tracking', 'gesture'],
      nodeClass: null,
      editorConfig: {
        showHandVisualization: true,
        showGestureRecognition: true,
        showHandSettings: true,
        customPanels: ['handViewer', 'gestureLibrary', 'handSettings', 'gestureTraining']
      }
    });

    console.log('手部追踪节点集成完成');
  }

  /**
   * 集成身体追踪节点
   */
  private integrateBodyTrackingNode(): void {
    this.registerNode({
      type: 'BodyTracking',
      name: '身体追踪',
      description: '全身追踪节点，综合骨骼、面部、手部追踪数据',
      category: 'MotionCapture/Tracking',
      icon: 'person',
      color: '#FF9800',
      tags: ['body', 'tracking', 'full'],
      nodeClass: null,
      editorConfig: {
        showFullBodyVisualization: true,
        showDataFusion: true,
        showMovementAnalysis: true,
        customPanels: ['bodyViewer', 'dataFusion', 'movementAnalyzer', 'poseEstimator']
      }
    });

    console.log('身体追踪节点集成完成');
  }

  /**
   * 注册节点到编辑器
   */
  private registerNode(nodeInfo: NodeInfo & { editorConfig?: any }): void {
    // 注册到节点编辑器
    this.nodeEditor.registerNode(nodeInfo);

    // 添加到已注册节点集合
    this.registeredNodes.add(nodeInfo.type);

    // 添加到分类映射
    const category = nodeInfo.category;
    if (!this.categoryNodes.has(category)) {
      this.categoryNodes.set(category, []);
    }
    this.categoryNodes.get(category)!.push(nodeInfo.type);

    // 如果有编辑器配置，设置自定义面板
    if (nodeInfo.editorConfig) {
      this.setupCustomPanels(nodeInfo.type, nodeInfo.editorConfig);
    }
  }

  /**
   * 设置自定义面板
   */
  private setupCustomPanels(nodeType: string, editorConfig: any): void {
    if (editorConfig.customPanels) {
      editorConfig.customPanels.forEach((panelType: string) => {
        this.nodeEditor.addCustomPanel(nodeType, panelType, this.createPanelConfig(panelType));
      });
    }
  }

  /**
   * 创建面板配置
   */
  private createPanelConfig(panelType: string): any {
    const panelConfigs: { [key: string]: any } = {
      deviceSelector: {
        title: '设备选择',
        icon: 'devices',
        resizable: true,
        collapsible: true
      },
      cameraSettings: {
        title: '摄像头设置',
        icon: 'settings',
        resizable: true,
        collapsible: true
      },
      videoPreview: {
        title: '视频预览',
        icon: 'videocam',
        resizable: true,
        collapsible: false
      },
      streamStatus: {
        title: '流状态',
        icon: 'info',
        resizable: true,
        collapsible: true
      },
      systemConfig: {
        title: '系统配置',
        icon: 'settings',
        resizable: true,
        collapsible: true
      },
      capabilitySettings: {
        title: '功能设置',
        icon: 'tune',
        resizable: true,
        collapsible: true
      },
      systemStatus: {
        title: '系统状态',
        icon: 'monitor',
        resizable: true,
        collapsible: false
      },
      performanceMonitor: {
        title: '性能监控',
        icon: 'speed',
        resizable: true,
        collapsible: true
      },
      skeletonViewer: {
        title: '骨骼查看器',
        icon: 'accessibility',
        resizable: true,
        collapsible: false
      },
      trackingSettings: {
        title: '追踪设置',
        icon: 'tune',
        resizable: true,
        collapsible: true
      },
      keypointList: {
        title: '关键点列表',
        icon: 'list',
        resizable: true,
        collapsible: true
      },
      qualityMonitor: {
        title: '质量监控',
        icon: 'analytics',
        resizable: true,
        collapsible: true
      },
      faceViewer: {
        title: '面部查看器',
        icon: 'face',
        resizable: true,
        collapsible: false
      },
      landmarkEditor: {
        title: '关键点编辑器',
        icon: 'edit',
        resizable: true,
        collapsible: true
      },
      expressionMonitor: {
        title: '表情监控',
        icon: 'sentiment_satisfied',
        resizable: true,
        collapsible: true
      },
      gazeTracker: {
        title: '视线追踪',
        icon: 'visibility',
        resizable: true,
        collapsible: true
      },
      handViewer: {
        title: '手部查看器',
        icon: 'pan_tool',
        resizable: true,
        collapsible: false
      },
      gestureLibrary: {
        title: '手势库',
        icon: 'library_books',
        resizable: true,
        collapsible: true
      },
      handSettings: {
        title: '手部设置',
        icon: 'settings',
        resizable: true,
        collapsible: true
      },
      gestureTraining: {
        title: '手势训练',
        icon: 'school',
        resizable: true,
        collapsible: true
      },
      bodyViewer: {
        title: '身体查看器',
        icon: 'person',
        resizable: true,
        collapsible: false
      },
      dataFusion: {
        title: '数据融合',
        icon: 'merge_type',
        resizable: true,
        collapsible: true
      },
      movementAnalyzer: {
        title: '运动分析器',
        icon: 'directions_run',
        resizable: true,
        collapsible: true
      },
      poseEstimator: {
        title: '姿态估计器',
        icon: 'accessibility_new',
        resizable: true,
        collapsible: true
      }
    };

    return panelConfigs[panelType] || {
      title: panelType,
      icon: 'extension',
      resizable: true,
      collapsible: true
    };
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 动作捕捉节点面板
    const motionCapturePalette = {
      category: '动作捕捉',
      nodes: Array.from(this.registeredNodes)
    };

    this.nodeEditor.addNodePalette(motionCapturePalette);
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    this.categoryNodes.forEach((nodes, category) => {
      this.nodeEditor.addNodeCategory({
        name: category,
        displayName: this.getCategoryDisplayName(category),
        icon: this.getCategoryIcon(category),
        color: '#FF9800',
        nodes: nodes
      });
    });
  }

  /**
   * 获取分类显示名称
   */
  private getCategoryDisplayName(category: string): string {
    const displayNames: { [key: string]: string } = {
      'MotionCapture/Input': '动捕输入',
      'MotionCapture/System': '动捕系统',
      'MotionCapture/Tracking': '动捕追踪'
    };

    return displayNames[category] || category;
  }

  /**
   * 获取分类图标
   */
  private getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'MotionCapture/Input': 'videocam',
      'MotionCapture/System': 'settings',
      'MotionCapture/Tracking': 'track_changes'
    };

    return icons[category] || 'videocam';
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): any {
    return {
      totalNodes: this.registeredNodes.size,
      nodesByCategory: Object.fromEntries(this.categoryNodes),
      categories: Array.from(this.categoryNodes.keys())
    };
  }
}
