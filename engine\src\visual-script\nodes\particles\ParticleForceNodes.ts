/**
 * 粒子力场和碰撞节点
 * 提供粒子力场编辑器和碰撞编辑器功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 力场类型枚举
 */
export enum ForceFieldType {
  GRAVITY = 'gravity',
  WIND = 'wind',
  VORTEX = 'vortex',
  RADIAL = 'radial',
  TURBULENCE = 'turbulence',
  MAGNETIC = 'magnetic',
  CUSTOM = 'custom'
}

/**
 * 碰撞类型枚举
 */
export enum CollisionType {
  PLANE = 'plane',
  SPHERE = 'sphere',
  BOX = 'box',
  MESH = 'mesh',
  TERRAIN = 'terrain'
}

/**
 * 粒子力场编辑器节点
 */
export class ParticleForceEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'ParticleForceEditor';
  public static readonly NAME = '粒子力场编辑器';
  public static readonly DESCRIPTION = '粒子力场编辑器，用于配置影响粒子的各种力场';

  private forceFields: Map<string, any> = new Map();

  constructor(nodeType: string = ParticleForceEditorNode.TYPE, name: string = ParticleForceEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createForceField', 'trigger', '创建力场');
    this.addInput('editForceField', 'trigger', '编辑力场');
    this.addInput('deleteForceField', 'trigger', '删除力场');
    this.addInput('forceFieldId', 'string', '力场ID');
    this.addInput('forceFieldName', 'string', '力场名称');
    this.addInput('forceType', 'string', '力场类型');
    this.addInput('strength', 'number', '力场强度');
    this.addInput('position', 'vector3', '力场位置');
    this.addInput('direction', 'vector3', '力场方向');
    this.addInput('radius', 'number', '影响半径');
    this.addInput('falloff', 'number', '衰减系数');

    // 输出端口
    this.addOutput('forceFieldId', 'string', '力场ID');
    this.addOutput('forceFieldData', 'object', '力场数据');
    this.addOutput('forceFieldCount', 'number', '力场数量');
    this.addOutput('onForceFieldCreated', 'trigger', '力场创建完成');
    this.addOutput('onForceFieldEdited', 'trigger', '力场编辑完成');
    this.addOutput('onForceFieldDeleted', 'trigger', '力场删除完成');
  }

  public execute(inputs: any): any {
    try {
      if (inputs?.createForceField) {
        return this.createForceField(inputs);
      } else if (inputs?.editForceField) {
        return this.editForceField(inputs);
      } else if (inputs?.deleteForceField) {
        return this.deleteForceField(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ParticleForceEditorNode', '力场编辑操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private createForceField(inputs: any): any {
    const forceFieldId = inputs?.forceFieldId || `force_${Date.now()}`;
    const forceFieldName = inputs?.forceFieldName || '新力场';
    const forceType = inputs?.forceType || ForceFieldType.GRAVITY;

    const forceFieldData = {
      id: forceFieldId,
      name: forceFieldName,
      type: forceType,
      strength: inputs?.strength || 1.0,
      position: inputs?.position || new Vector3(0, 0, 0),
      direction: inputs?.direction || new Vector3(0, -1, 0),
      radius: inputs?.radius || 10.0,
      falloff: inputs?.falloff || 1.0,
      enabled: true,
      createdAt: Date.now(),
      modifiedAt: Date.now()
    };

    this.forceFields.set(forceFieldId, forceFieldData);

    Debug.log('ParticleForceEditorNode', `力场创建成功: ${forceFieldName} (${forceFieldId})`);

    return {
      forceFieldId,
      forceFieldData,
      forceFieldCount: this.forceFields.size,
      onForceFieldCreated: true,
      onForceFieldEdited: false,
      onForceFieldDeleted: false
    };
  }

  private editForceField(inputs: any): any {
    const forceFieldId = inputs?.forceFieldId as string;
    const forceField = this.forceFields.get(forceFieldId);

    if (!forceField) {
      Debug.warn('ParticleForceEditorNode', `力场不存在: ${forceFieldId}`);
      return this.getDefaultOutputs();
    }

    // 更新力场属性
    if (inputs?.forceFieldName) forceField.name = inputs.forceFieldName;
    if (inputs?.forceType) forceField.type = inputs.forceType;
    if (inputs?.strength !== undefined) forceField.strength = inputs.strength;
    if (inputs?.position) forceField.position = inputs.position;
    if (inputs?.direction) forceField.direction = inputs.direction;
    if (inputs?.radius !== undefined) forceField.radius = inputs.radius;
    if (inputs?.falloff !== undefined) forceField.falloff = inputs.falloff;

    forceField.modifiedAt = Date.now();

    Debug.log('ParticleForceEditorNode', `力场编辑成功: ${forceFieldId}`);

    return {
      forceFieldId,
      forceFieldData: forceField,
      forceFieldCount: this.forceFields.size,
      onForceFieldCreated: false,
      onForceFieldEdited: true,
      onForceFieldDeleted: false
    };
  }

  private deleteForceField(inputs: any): any {
    const forceFieldId = inputs?.forceFieldId as string;

    if (!forceFieldId || !this.forceFields.has(forceFieldId)) {
      Debug.warn('ParticleForceEditorNode', `力场不存在: ${forceFieldId}`);
      return this.getDefaultOutputs();
    }

    this.forceFields.delete(forceFieldId);

    Debug.log('ParticleForceEditorNode', `力场删除成功: ${forceFieldId}`);

    return {
      forceFieldId,
      forceFieldData: null,
      forceFieldCount: this.forceFields.size,
      onForceFieldCreated: false,
      onForceFieldEdited: false,
      onForceFieldDeleted: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      forceFieldId: '',
      forceFieldData: null,
      forceFieldCount: this.forceFields.size,
      onForceFieldCreated: false,
      onForceFieldEdited: false,
      onForceFieldDeleted: false
    };
  }

  /**
   * 获取所有力场
   */
  public getAllForceFields(): Map<string, any> {
    return new Map(this.forceFields);
  }

  /**
   * 清除所有力场
   */
  public clearAllForceFields(): void {
    this.forceFields.clear();
    Debug.log('ParticleForceEditorNode', '所有力场已清除');
  }
}

/**
 * 粒子碰撞编辑器节点
 */
export class ParticleCollisionEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'ParticleCollisionEditor';
  public static readonly NAME = '粒子碰撞编辑器';
  public static readonly DESCRIPTION = '粒子碰撞编辑器，用于配置粒子与环境的碰撞检测';

  private colliders: Map<string, any> = new Map();

  constructor(nodeType: string = ParticleCollisionEditorNode.TYPE, name: string = ParticleCollisionEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createCollider', 'trigger', '创建碰撞器');
    this.addInput('editCollider', 'trigger', '编辑碰撞器');
    this.addInput('deleteCollider', 'trigger', '删除碰撞器');
    this.addInput('colliderId', 'string', '碰撞器ID');
    this.addInput('colliderName', 'string', '碰撞器名称');
    this.addInput('collisionType', 'string', '碰撞类型');
    this.addInput('position', 'vector3', '碰撞器位置');
    this.addInput('size', 'vector3', '碰撞器尺寸');
    this.addInput('bounciness', 'number', '弹性系数');
    this.addInput('friction', 'number', '摩擦系数');
    this.addInput('damping', 'number', '阻尼系数');

    // 输出端口
    this.addOutput('colliderId', 'string', '碰撞器ID');
    this.addOutput('colliderData', 'object', '碰撞器数据');
    this.addOutput('colliderCount', 'number', '碰撞器数量');
    this.addOutput('onColliderCreated', 'trigger', '碰撞器创建完成');
    this.addOutput('onColliderEdited', 'trigger', '碰撞器编辑完成');
    this.addOutput('onColliderDeleted', 'trigger', '碰撞器删除完成');
  }

  public execute(inputs: any): any {
    try {
      if (inputs?.createCollider) {
        return this.createCollider(inputs);
      } else if (inputs?.editCollider) {
        return this.editCollider(inputs);
      } else if (inputs?.deleteCollider) {
        return this.deleteCollider(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ParticleCollisionEditorNode', '碰撞器编辑操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private createCollider(inputs: any): any {
    const colliderId = inputs?.colliderId || `collider_${Date.now()}`;
    const colliderName = inputs?.colliderName || '新碰撞器';
    const collisionType = inputs?.collisionType || CollisionType.PLANE;

    const colliderData = {
      id: colliderId,
      name: colliderName,
      type: collisionType,
      position: inputs?.position || new Vector3(0, 0, 0),
      size: inputs?.size || new Vector3(1, 1, 1),
      bounciness: inputs?.bounciness || 0.5,
      friction: inputs?.friction || 0.3,
      damping: inputs?.damping || 0.1,
      enabled: true,
      createdAt: Date.now(),
      modifiedAt: Date.now()
    };

    this.colliders.set(colliderId, colliderData);

    Debug.log('ParticleCollisionEditorNode', `碰撞器创建成功: ${colliderName} (${colliderId})`);

    return {
      colliderId,
      colliderData,
      colliderCount: this.colliders.size,
      onColliderCreated: true,
      onColliderEdited: false,
      onColliderDeleted: false
    };
  }

  private editCollider(inputs: any): any {
    const colliderId = inputs?.colliderId as string;
    const collider = this.colliders.get(colliderId);

    if (!collider) {
      Debug.warn('ParticleCollisionEditorNode', `碰撞器不存在: ${colliderId}`);
      return this.getDefaultOutputs();
    }

    // 更新碰撞器属性
    if (inputs?.colliderName) collider.name = inputs.colliderName;
    if (inputs?.collisionType) collider.type = inputs.collisionType;
    if (inputs?.position) collider.position = inputs.position;
    if (inputs?.size) collider.size = inputs.size;
    if (inputs?.bounciness !== undefined) collider.bounciness = inputs.bounciness;
    if (inputs?.friction !== undefined) collider.friction = inputs.friction;
    if (inputs?.damping !== undefined) collider.damping = inputs.damping;

    collider.modifiedAt = Date.now();

    Debug.log('ParticleCollisionEditorNode', `碰撞器编辑成功: ${colliderId}`);

    return {
      colliderId,
      colliderData: collider,
      colliderCount: this.colliders.size,
      onColliderCreated: false,
      onColliderEdited: true,
      onColliderDeleted: false
    };
  }

  private deleteCollider(inputs: any): any {
    const colliderId = inputs?.colliderId as string;

    if (!colliderId || !this.colliders.has(colliderId)) {
      Debug.warn('ParticleCollisionEditorNode', `碰撞器不存在: ${colliderId}`);
      return this.getDefaultOutputs();
    }

    this.colliders.delete(colliderId);

    Debug.log('ParticleCollisionEditorNode', `碰撞器删除成功: ${colliderId}`);

    return {
      colliderId,
      colliderData: null,
      colliderCount: this.colliders.size,
      onColliderCreated: false,
      onColliderEdited: false,
      onColliderDeleted: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      colliderId: '',
      colliderData: null,
      colliderCount: this.colliders.size,
      onColliderCreated: false,
      onColliderEdited: false,
      onColliderDeleted: false
    };
  }

  /**
   * 获取所有碰撞器
   */
  public getAllColliders(): Map<string, any> {
    return new Map(this.colliders);
  }

  /**
   * 清除所有碰撞器
   */
  public clearAllColliders(): void {
    this.colliders.clear();
    Debug.log('ParticleCollisionEditorNode', '所有碰撞器已清除');
  }
}
