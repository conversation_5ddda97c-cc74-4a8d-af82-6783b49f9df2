/**
 * 动画状态机面板组件
 * 为AnimationStateMachineNode提供专用的编辑界面
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Tree,
  Button,
  Input,
  Select,
  Slider,
  Switch,
  Tabs,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Progress,
  Tag,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  message
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 动画状态接口
 */
interface AnimationState {
  id: string;
  name: string;
  animationClip: string;
  speed: number;
  loop: boolean;
  weight: number;
  enabled: boolean;
}

/**
 * 动画过渡接口
 */
interface AnimationTransition {
  id: string;
  fromState: string;
  toState: string;
  duration: number;
  conditions: TransitionCondition[];
  enabled: boolean;
}

/**
 * 过渡条件接口
 */
interface TransitionCondition {
  id: string;
  parameter: string;
  operator: 'equals' | 'greater' | 'less' | 'greaterEqual' | 'lessEqual';
  value: number | boolean | string;
}

/**
 * 动画参数接口
 */
interface AnimationParameter {
  id: string;
  name: string;
  type: 'float' | 'int' | 'bool' | 'trigger';
  value: number | boolean;
  defaultValue: number | boolean;
}

/**
 * 动画状态机面板属性
 */
interface AnimationStateMachinePanelProps {
  nodeId?: string;
  onStateChange?: (states: AnimationState[]) => void;
  onTransitionChange?: (transitions: AnimationTransition[]) => void;
  onParameterChange?: (parameters: AnimationParameter[]) => void;
  readonly?: boolean;
}

/**
 * 动画状态机面板组件
 */
export const AnimationStateMachinePanel: React.FC<AnimationStateMachinePanelProps> = ({
  nodeId,
  onStateChange,
  onTransitionChange,
  onParameterChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  
  // 状态管理
  const [states, setStates] = useState<AnimationState[]>([]);
  const [transitions, setTransitions] = useState<AnimationTransition[]>([]);
  const [parameters, setParameters] = useState<AnimationParameter[]>([]);
  const [currentState, setCurrentState] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  
  // UI状态
  const [selectedState, setSelectedState] = useState<string>('');
  const [selectedTransition, setSelectedTransition] = useState<string>('');
  const [showStateModal, setShowStateModal] = useState(false);
  const [showTransitionModal, setShowTransitionModal] = useState(false);
  const [showParameterModal, setShowParameterModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  // 表单实例
  const [stateForm] = Form.useForm();
  const [transitionForm] = Form.useForm();
  const [parameterForm] = Form.useForm();

  /**
   * 初始化默认数据
   */
  useEffect(() => {
    const defaultStates: AnimationState[] = [
      {
        id: 'idle',
        name: '待机',
        animationClip: 'idle_animation',
        speed: 1.0,
        loop: true,
        weight: 1.0,
        enabled: true
      },
      {
        id: 'walk',
        name: '行走',
        animationClip: 'walk_animation',
        speed: 1.0,
        loop: true,
        weight: 1.0,
        enabled: true
      },
      {
        id: 'run',
        name: '跑步',
        animationClip: 'run_animation',
        speed: 1.2,
        loop: true,
        weight: 1.0,
        enabled: true
      }
    ];

    const defaultTransitions: AnimationTransition[] = [
      {
        id: 'idle_to_walk',
        fromState: 'idle',
        toState: 'walk',
        duration: 0.3,
        conditions: [
          {
            id: 'speed_condition',
            parameter: 'speed',
            operator: 'greater',
            value: 0.1
          }
        ],
        enabled: true
      },
      {
        id: 'walk_to_run',
        fromState: 'walk',
        toState: 'run',
        duration: 0.2,
        conditions: [
          {
            id: 'speed_condition',
            parameter: 'speed',
            operator: 'greater',
            value: 5.0
          }
        ],
        enabled: true
      }
    ];

    const defaultParameters: AnimationParameter[] = [
      {
        id: 'speed',
        name: '速度',
        type: 'float',
        value: 0,
        defaultValue: 0
      },
      {
        id: 'is_grounded',
        name: '是否着地',
        type: 'bool',
        value: true,
        defaultValue: true
      },
      {
        id: 'jump_trigger',
        name: '跳跃触发',
        type: 'trigger',
        value: false,
        defaultValue: false
      }
    ];

    setStates(defaultStates);
    setTransitions(defaultTransitions);
    setParameters(defaultParameters);
    setCurrentState('idle');
  }, []);

  /**
   * 播放控制
   */
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    message.success('动画状态机开始播放');
  }, []);

  const handlePause = useCallback(() => {
    setIsPlaying(false);
    message.info('动画状态机暂停');
  }, []);

  const handleStop = useCallback(() => {
    setIsPlaying(false);
    setCurrentState('idle');
    message.info('动画状态机停止');
  }, []);

  /**
   * 状态管理
   */
  const handleAddState = useCallback(() => {
    setEditingItem(null);
    stateForm.resetFields();
    setShowStateModal(true);
  }, [stateForm]);

  const handleEditState = useCallback((state: AnimationState) => {
    setEditingItem(state);
    stateForm.setFieldsValue(state);
    setShowStateModal(true);
  }, [stateForm]);

  const handleDeleteState = useCallback((stateId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个动画状态吗？',
      onOk: () => {
        const newStates = states.filter(s => s.id !== stateId);
        setStates(newStates);
        onStateChange?.(newStates);
        message.success('动画状态已删除');
      }
    });
  }, [states, onStateChange]);

  /**
   * 过渡管理
   */
  const handleAddTransition = useCallback(() => {
    setEditingItem(null);
    transitionForm.resetFields();
    setShowTransitionModal(true);
  }, [transitionForm]);

  const handleEditTransition = useCallback((transition: AnimationTransition) => {
    setEditingItem(transition);
    transitionForm.setFieldsValue(transition);
    setShowTransitionModal(true);
  }, [transitionForm]);

  /**
   * 参数管理
   */
  const handleAddParameter = useCallback(() => {
    setEditingItem(null);
    parameterForm.resetFields();
    setShowParameterModal(true);
  }, [parameterForm]);

  const handleParameterValueChange = useCallback((parameterId: string, value: any) => {
    const newParameters = parameters.map(p => 
      p.id === parameterId ? { ...p, value } : p
    );
    setParameters(newParameters);
    onParameterChange?.(newParameters);
  }, [parameters, onParameterChange]);

  /**
   * 渲染状态列表
   */
  const renderStateList = () => (
    <Card 
      title="动画状态" 
      size="small"
      extra={
        !readonly && (
          <Button 
            type="primary" 
            size="small" 
            icon={<PlusOutlined />}
            onClick={handleAddState}
          >
            添加状态
          </Button>
        )
      }
    >
      {states.map(state => (
        <Card 
          key={state.id}
          size="small"
          style={{ marginBottom: 8 }}
          className={currentState === state.id ? 'current-state' : ''}
        >
          <Row align="middle" justify="space-between">
            <Col>
              <Space>
                <Tag color={state.enabled ? 'green' : 'red'}>
                  {state.name}
                </Tag>
                <Text type="secondary">{state.animationClip}</Text>
              </Space>
            </Col>
            <Col>
              {!readonly && (
                <Space>
                  <Button 
                    size="small" 
                    icon={<EditOutlined />}
                    onClick={() => handleEditState(state)}
                  />
                  <Button 
                    size="small" 
                    danger 
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteState(state.id)}
                  />
                </Space>
              )}
            </Col>
          </Row>
          <Row style={{ marginTop: 8 }}>
            <Col span={8}>
              <Text type="secondary">速度: {state.speed}</Text>
            </Col>
            <Col span={8}>
              <Text type="secondary">权重: {state.weight}</Text>
            </Col>
            <Col span={8}>
              <Text type="secondary">循环: {state.loop ? '是' : '否'}</Text>
            </Col>
          </Row>
        </Card>
      ))}
    </Card>
  );

  /**
   * 渲染过渡列表
   */
  const renderTransitionList = () => (
    <Card 
      title="状态过渡" 
      size="small"
      extra={
        !readonly && (
          <Button 
            type="primary" 
            size="small" 
            icon={<PlusOutlined />}
            onClick={handleAddTransition}
          >
            添加过渡
          </Button>
        )
      }
    >
      {transitions.map(transition => (
        <Card key={transition.id} size="small" style={{ marginBottom: 8 }}>
          <Row align="middle" justify="space-between">
            <Col>
              <Space>
                <Text strong>{transition.fromState}</Text>
                <BranchesOutlined />
                <Text strong>{transition.toState}</Text>
                <Tag>{transition.duration}s</Tag>
              </Space>
            </Col>
            <Col>
              {!readonly && (
                <Space>
                  <Button 
                    size="small" 
                    icon={<EditOutlined />}
                    onClick={() => handleEditTransition(transition)}
                  />
                  <Button size="small" danger icon={<DeleteOutlined />} />
                </Space>
              )}
            </Col>
          </Row>
          <div style={{ marginTop: 8 }}>
            <Text type="secondary">
              条件: {transition.conditions.length} 个
            </Text>
          </div>
        </Card>
      ))}
    </Card>
  );

  /**
   * 渲染参数控制
   */
  const renderParameterControls = () => (
    <Card 
      title="动画参数" 
      size="small"
      extra={
        !readonly && (
          <Button 
            type="primary" 
            size="small" 
            icon={<PlusOutlined />}
            onClick={handleAddParameter}
          >
            添加参数
          </Button>
        )
      }
    >
      {parameters.map(param => (
        <Row key={param.id} align="middle" style={{ marginBottom: 12 }}>
          <Col span={8}>
            <Text strong>{param.name}</Text>
            <br />
            <Tag size="small">{param.type}</Tag>
          </Col>
          <Col span={16}>
            {param.type === 'float' && (
              <Slider
                min={0}
                max={10}
                step={0.1}
                value={param.value as number}
                onChange={(value) => handleParameterValueChange(param.id, value)}
                disabled={readonly}
              />
            )}
            {param.type === 'int' && (
              <InputNumber
                min={0}
                max={100}
                value={param.value as number}
                onChange={(value) => handleParameterValueChange(param.id, value)}
                disabled={readonly}
              />
            )}
            {param.type === 'bool' && (
              <Switch
                checked={param.value as boolean}
                onChange={(checked) => handleParameterValueChange(param.id, checked)}
                disabled={readonly}
              />
            )}
            {param.type === 'trigger' && (
              <Button
                type="primary"
                size="small"
                onClick={() => handleParameterValueChange(param.id, true)}
                disabled={readonly}
              >
                触发
              </Button>
            )}
          </Col>
        </Row>
      ))}
    </Card>
  );

  /**
   * 渲染播放控制
   */
  const renderPlaybackControls = () => (
    <Card title="播放控制" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Space>
              <Button
                type={isPlaying ? "default" : "primary"}
                icon={<PlayCircleOutlined />}
                onClick={handlePlay}
                disabled={readonly}
              >
                播放
              </Button>
              <Button
                icon={<PauseCircleOutlined />}
                onClick={handlePause}
                disabled={readonly}
              >
                暂停
              </Button>
              <Button
                icon={<StopOutlined />}
                onClick={handleStop}
                disabled={readonly}
              >
                停止
              </Button>
            </Space>
          </Col>
          <Col>
            <Tag color={isPlaying ? 'green' : 'default'}>
              {isPlaying ? '播放中' : '已停止'}
            </Tag>
          </Col>
        </Row>
        
        <Row align="middle">
          <Col span={6}>
            <Text>播放速度:</Text>
          </Col>
          <Col span={18}>
            <Slider
              min={0.1}
              max={3.0}
              step={0.1}
              value={playbackSpeed}
              onChange={setPlaybackSpeed}
              disabled={readonly}
            />
          </Col>
        </Row>
        
        <Row align="middle">
          <Col span={6}>
            <Text>当前状态:</Text>
          </Col>
          <Col span={18}>
            <Tag color="blue">{currentState}</Tag>
          </Col>
        </Row>
      </Space>
    </Card>
  );

  return (
    <div className="animation-state-machine-panel">
      <Card
        title={
          <Space>
            <NodeIndexOutlined />
            <Title level={4} style={{ margin: 0 }}>
              动画状态机
            </Title>
          </Space>
        }
        size="small"
      >
        <Tabs defaultActiveKey="states" size="small">
          <TabPane tab="状态" key="states">
            {renderStateList()}
          </TabPane>
          <TabPane tab="过渡" key="transitions">
            {renderTransitionList()}
          </TabPane>
          <TabPane tab="参数" key="parameters">
            {renderParameterControls()}
          </TabPane>
          <TabPane tab="控制" key="controls">
            {renderPlaybackControls()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 状态编辑模态框 */}
      <Modal
        title={editingItem ? "编辑动画状态" : "添加动画状态"}
        visible={showStateModal}
        onCancel={() => setShowStateModal(false)}
        onOk={() => stateForm.submit()}
      >
        <Form
          form={stateForm}
          layout="vertical"
          onFinish={(values) => {
            const newState: AnimationState = {
              id: editingItem?.id || `state_${Date.now()}`,
              ...values
            };
            
            let newStates;
            if (editingItem) {
              newStates = states.map(s => s.id === editingItem.id ? newState : s);
            } else {
              newStates = [...states, newState];
            }
            
            setStates(newStates);
            onStateChange?.(newStates);
            setShowStateModal(false);
            message.success(editingItem ? '状态已更新' : '状态已添加');
          }}
        >
          <Form.Item name="name" label="状态名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="animationClip" label="动画片段" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="speed" label="播放速度" initialValue={1.0}>
            <InputNumber min={0.1} max={5.0} step={0.1} />
          </Form.Item>
          <Form.Item name="weight" label="权重" initialValue={1.0}>
            <InputNumber min={0} max={1} step={0.1} />
          </Form.Item>
          <Form.Item name="loop" label="循环播放" valuePropName="checked" initialValue={true}>
            <Switch />
          </Form.Item>
          <Form.Item name="enabled" label="启用" valuePropName="checked" initialValue={true}>
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AnimationStateMachinePanel;
