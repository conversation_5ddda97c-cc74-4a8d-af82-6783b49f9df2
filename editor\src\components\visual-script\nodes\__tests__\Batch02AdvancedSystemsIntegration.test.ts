/**
 * 批次0.2高级系统节点集成测试
 */

import { Batch02AdvancedSystemsIntegration } from '../Batch02AdvancedSystemsIntegration';
import { Batch02AdvancedSystemsIntegrationManager } from '../../../integration/Batch02AdvancedSystemsIntegration';

// 模拟NodeEditor
const mockNodeEditor = {
  addNodeToPalette: jest.fn(),
  addNodeCategory: jest.fn(),
  registerNodeType: jest.fn(),
  refreshNodePalette: jest.fn(),
  createNode: jest.fn(),
  addNode: jest.fn(),
  selectNode: jest.fn()
};

describe('批次0.2高级系统节点集成测试', () => {
  let integration: Batch02AdvancedSystemsIntegration;
  let manager: Batch02AdvancedSystemsIntegrationManager;

  beforeEach(() => {
    jest.clearAllMocks();
    integration = new Batch02AdvancedSystemsIntegration(mockNodeEditor as any);
    manager = new Batch02AdvancedSystemsIntegrationManager(mockNodeEditor as any);
  });

  describe('Batch02AdvancedSystemsIntegration', () => {
    test('应该成功创建集成实例', () => {
      expect(integration).toBeInstanceOf(Batch02AdvancedSystemsIntegration);
    });

    test('应该集成所有68个高级系统节点', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      expect(registeredNodes.size).toBe(68);
    });

    test('应该创建正确的节点分类', () => {
      integration.integrateAllNodes();
      
      const categories = integration.getNodeCategories();
      expect(categories.size).toBe(9); // 9个分类
      
      // 验证关键分类存在
      expect(categories.has('Input/Advanced')).toBe(true);
      expect(categories.has('Input/Sensors')).toBe(true);
      expect(categories.has('Input/VRAR')).toBe(true);
      expect(categories.has('Animation/Advanced')).toBe(true);
      expect(categories.has('Animation/StateMachine')).toBe(true);
      expect(categories.has('Audio/Advanced')).toBe(true);
      expect(categories.has('Audio/Effects')).toBe(true);
      expect(categories.has('Physics/Advanced')).toBe(true);
      expect(categories.has('Physics/Simulation')).toBe(true);
    });

    test('应该正确分配节点到各个分类', () => {
      integration.integrateAllNodes();
      
      const categories = integration.getNodeCategories();
      
      // 验证高级输入分类节点数量
      const advancedInputNodes = categories.get('Input/Advanced') || [];
      expect(advancedInputNodes.length).toBe(10);
      
      // 验证传感器分类节点数量
      const sensorNodes = categories.get('Input/Sensors') || [];
      expect(sensorNodes.length).toBe(10);
      
      // 验证VR/AR输入分类节点数量
      const vrArNodes = categories.get('Input/VRAR') || [];
      expect(vrArNodes.length).toBe(5);
    });

    test('应该注册关键节点', () => {
      integration.integrateAllNodes();
      
      const keyNodes = [
        'MultiTouchNode',
        'GestureRecognitionNode',
        'AccelerometerNode',
        'VRControllerInputNode',
        'AnimationStateMachineNode',
        'IKSystemNode',
        'AudioMixerNode',
        'SpatialAudioNode',
        'SoftBodyPhysicsNode',
        'FluidSimulationNode'
      ];

      keyNodes.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该为节点设置正确的UI配置', () => {
      integration.integrateAllNodes();
      
      // 测试多点触控节点的UI配置
      const multiTouchConfig = integration.getNodeConfig('MultiTouchNode');
      expect(multiTouchConfig?.uiConfig?.hasCustomPanel).toBe(true);
      expect(multiTouchConfig?.uiConfig?.hasDataVisualization).toBe(true);
      expect(multiTouchConfig?.uiConfig?.hasParameterEditor).toBe(true);
      
      // 测试动画状态机节点的UI配置
      const stateMachineConfig = integration.getNodeConfig('AnimationStateMachineNode');
      expect(stateMachineConfig?.uiConfig?.hasCustomPanel).toBe(true);
      expect(stateMachineConfig?.uiConfig?.panelComponent).toBe('AnimationStateMachinePanel');
      
      // 测试音频混合器节点的UI配置
      const audioMixerConfig = integration.getNodeConfig('AudioMixerNode');
      expect(audioMixerConfig?.uiConfig?.hasCustomPanel).toBe(true);
      expect(audioMixerConfig?.uiConfig?.panelComponent).toBe('AudioMixerPanel');
    });

    test('应该生成正确的集成统计信息', () => {
      integration.integrateAllNodes();
      
      const stats = integration.getIntegrationStats();
      expect(stats.totalNodes).toBe(68);
      expect(stats.categories).toBe(9);
      expect(stats.nodesByCategory).toHaveProperty('Input/Advanced', 10);
      expect(stats.nodesByCategory).toHaveProperty('Input/Sensors', 10);
      expect(stats.nodesByCategory).toHaveProperty('Input/VRAR', 5);
      expect(stats.nodesByCategory).toHaveProperty('Animation/StateMachine', 8);
      expect(stats.nodesByCategory).toHaveProperty('Animation/Advanced', 7);
      expect(stats.nodesByCategory).toHaveProperty('Audio/Effects', 8);
      expect(stats.nodesByCategory).toHaveProperty('Audio/Advanced', 5);
      expect(stats.nodesByCategory).toHaveProperty('Physics/Simulation', 8);
      expect(stats.nodesByCategory).toHaveProperty('Physics/Advanced', 7);
    });
  });

  describe('Batch02AdvancedSystemsIntegrationManager', () => {
    test('应该成功创建管理器实例', () => {
      expect(manager).toBeInstanceOf(Batch02AdvancedSystemsIntegrationManager);
    });

    test('应该初始化正确的集成状态', () => {
      const status = manager.getIntegrationStatus();
      expect(status.completed).toBe(false);
      expect(status.totalNodes).toBe(68);
      expect(status.integratedNodes).toBe(0);
      expect(status.categories).toEqual([]);
      expect(status.errors).toEqual([]);
    });

    test('应该能够检查节点集成状态', () => {
      // 初始状态下节点未集成
      expect(manager.isNodeIntegrated('MultiTouchNode')).toBe(false);
      
      // 执行集成后应该能检测到节点
      // 注意：这里需要模拟集成过程，实际测试中可能需要更复杂的设置
    });

    test('应该能够获取节点配置', () => {
      // 初始状态下应该返回undefined
      expect(manager.getNodeConfig('MultiTouchNode')).toBeUndefined();
    });

    test('应该能够获取已注册的节点列表', () => {
      const nodes = manager.getRegisteredNodes();
      expect(Array.isArray(nodes)).toBe(true);
      // 初始状态下应该为空
      expect(nodes.length).toBe(0);
    });

    test('应该能够获取节点分类', () => {
      const categories = manager.getNodeCategories();
      expect(typeof categories).toBe('object');
      // 初始状态下应该为空对象
      expect(Object.keys(categories).length).toBe(0);
    });

    test('应该能够清理资源', () => {
      manager.cleanup();
      
      const status = manager.getIntegrationStatus();
      expect(status.completed).toBe(false);
      expect(status.integratedNodes).toBe(0);
      expect(status.categories).toEqual([]);
      expect(status.errors).toEqual([]);
    });
  });

  describe('节点显示名称和描述', () => {
    test('应该为所有节点提供中文显示名称', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      
      for (const [nodeType, config] of registeredNodes.entries()) {
        expect(config.name).toBeTruthy();
        expect(config.name).not.toBe(nodeType);
        expect(config.name.length).toBeGreaterThan(0);
        
        // 验证是否包含中文字符
        const hasChinese = /[\u4e00-\u9fa5]/.test(config.name);
        expect(hasChinese).toBe(true);
      }
    });

    test('应该为所有节点提供详细描述', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      
      for (const [nodeType, config] of registeredNodes.entries()) {
        expect(config.description).toBeTruthy();
        expect(config.description.length).toBeGreaterThan(10);
        
        // 验证是否包含中文字符
        const hasChinese = /[\u4e00-\u9fa5]/.test(config.description);
        expect(hasChinese).toBe(true);
      }
    });
  });

  describe('节点分类和标签', () => {
    test('应该为所有节点设置正确的分类', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      
      for (const [nodeType, config] of registeredNodes.entries()) {
        expect(config.category).toBeTruthy();
        expect(config.category.includes('/')).toBe(true); // 应该有层级结构
      }
    });

    test('应该为所有节点设置标签', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      
      for (const [nodeType, config] of registeredNodes.entries()) {
        expect(Array.isArray(config.tags)).toBe(true);
        expect(config.tags.length).toBeGreaterThan(0);
        expect(config.tags.includes('batch02')).toBe(true); // 应该包含批次标签
      }
    });

    test('应该为所有节点设置图标和颜色', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      
      for (const [nodeType, config] of registeredNodes.entries()) {
        expect(config.icon).toBeTruthy();
        expect(config.color).toBeTruthy();
        expect(config.color.startsWith('#')).toBe(true); // 应该是十六进制颜色
      }
    });
  });
});
