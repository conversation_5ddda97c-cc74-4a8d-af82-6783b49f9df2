/**
 * 批次0.2高级系统节点使用示例
 * 演示如何使用68个高级系统节点进行应用开发
 */

import { NodeEditor } from '../src/components/visual-script/NodeEditor';
import { 
  createBatch02AdvancedSystemsIntegration,
  Batch02AdvancedSystemsIntegrationManager 
} from '../src/integration/Batch02AdvancedSystemsIntegration';

/**
 * 高级输入系统示例
 */
class AdvancedInputSystemExample {
  private integrationManager: Batch02AdvancedSystemsIntegrationManager;

  constructor(integrationManager: Batch02AdvancedSystemsIntegrationManager) {
    this.integrationManager = integrationManager;
  }

  /**
   * 创建多点触控手势识别应用
   */
  createMultiTouchGestureApp(): void {
    console.log('🎯 创建多点触控手势识别应用...');

    // 检查所需节点是否已集成
    const requiredNodes = [
      'MultiTouchNode',
      'GestureRecognitionNode',
      'PinchGestureNode',
      'SwipeGestureNode',
      'RotationGestureNode'
    ];

    const missingNodes = requiredNodes.filter(node => 
      !this.integrationManager.isNodeIntegrated(node)
    );

    if (missingNodes.length > 0) {
      console.error('❌ 缺少必要的节点:', missingNodes);
      return;
    }

    console.log('✅ 所有必要节点已集成，可以创建多点触控应用');
    
    // 这里可以添加实际的节点创建和连接逻辑
    // 例如：创建节点图、设置参数、连接输入输出等
  }

  /**
   * 创建传感器数据监控应用
   */
  createSensorMonitoringApp(): void {
    console.log('📊 创建传感器数据监控应用...');

    const sensorNodes = [
      'AccelerometerNode',
      'GyroscopeNode',
      'CompassNode',
      'LightSensorNode',
      'TemperatureSensorNode'
    ];

    const availableNodes = sensorNodes.filter(node => 
      this.integrationManager.isNodeIntegrated(node)
    );

    console.log(`✅ 可用传感器节点: ${availableNodes.length}/${sensorNodes.length}`);
    console.log('📋 可用节点列表:', availableNodes);
  }

  /**
   * 创建VR/AR交互应用
   */
  createVRARInteractionApp(): void {
    console.log('🥽 创建VR/AR交互应用...');

    const vrArNodes = [
      'VRControllerInputNode',
      'VRHeadsetTrackingNode',
      'ARTouchInputNode',
      'EyeTrackingInputNode',
      'HandTrackingInputNode'
    ];

    vrArNodes.forEach(nodeType => {
      const config = this.integrationManager.getNodeConfig(nodeType);
      if (config) {
        console.log(`📝 ${nodeType}: ${config.name} - ${config.description}`);
      }
    });
  }
}

/**
 * 高级动画系统示例
 */
class AdvancedAnimationSystemExample {
  private integrationManager: Batch02AdvancedSystemsIntegrationManager;

  constructor(integrationManager: Batch02AdvancedSystemsIntegrationManager) {
    this.integrationManager = integrationManager;
  }

  /**
   * 创建动画状态机应用
   */
  createAnimationStateMachineApp(): void {
    console.log('🎭 创建动画状态机应用...');

    const stateMachineNodes = [
      'AnimationStateMachineNode',
      'AnimationStateNode',
      'AnimationTransitionNode',
      'AnimationBlendTreeNode',
      'AnimationParameterNode'
    ];

    const nodeConfigs = stateMachineNodes.map(nodeType => {
      const config = this.integrationManager.getNodeConfig(nodeType);
      return config ? { type: nodeType, config } : null;
    }).filter(Boolean);

    console.log(`✅ 动画状态机节点配置: ${nodeConfigs.length} 个`);
    
    // 检查是否有自定义面板配置
    nodeConfigs.forEach(item => {
      if (item?.config.uiConfig?.hasCustomPanel) {
        console.log(`🎨 ${item.type} 具有自定义面板: ${item.config.uiConfig.panelComponent || '默认面板'}`);
      }
    });
  }

  /**
   * 创建IK系统应用
   */
  createIKSystemApp(): void {
    console.log('🦴 创建IK系统应用...');

    const ikNodes = [
      'IKSystemNode',
      'IKChainNode',
      'IKConstraintNode'
    ];

    ikNodes.forEach(nodeType => {
      if (this.integrationManager.isNodeIntegrated(nodeType)) {
        const config = this.integrationManager.getNodeConfig(nodeType);
        console.log(`✅ ${config?.name}: ${config?.description}`);
        
        if (config?.uiConfig?.hasDataVisualization) {
          console.log(`📊 ${nodeType} 支持数据可视化`);
        }
      }
    });
  }
}

/**
 * 高级音频系统示例
 */
class AdvancedAudioSystemExample {
  private integrationManager: Batch02AdvancedSystemsIntegrationManager;

  constructor(integrationManager: Batch02AdvancedSystemsIntegrationManager) {
    this.integrationManager = integrationManager;
  }

  /**
   * 创建音频混合器应用
   */
  createAudioMixerApp(): void {
    console.log('🎵 创建音频混合器应用...');

    const audioMixerConfig = this.integrationManager.getNodeConfig('AudioMixerNode');
    if (audioMixerConfig) {
      console.log(`✅ 音频混合器: ${audioMixerConfig.name}`);
      console.log(`📝 描述: ${audioMixerConfig.description}`);
      
      if (audioMixerConfig.uiConfig?.hasCustomPanel) {
        console.log(`🎛️ 自定义面板: ${audioMixerConfig.uiConfig.panelComponent}`);
      }
    }

    // 检查音频效果节点
    const effectNodes = [
      'AudioEffectChainNode',
      'AudioReverbNode',
      'AudioEQNode',
      'AudioCompressorNode'
    ];

    console.log('🎚️ 可用音频效果:');
    effectNodes.forEach(nodeType => {
      if (this.integrationManager.isNodeIntegrated(nodeType)) {
        const config = this.integrationManager.getNodeConfig(nodeType);
        console.log(`  - ${config?.name}`);
      }
    });
  }

  /**
   * 创建3D空间音频应用
   */
  create3DSpatialAudioApp(): void {
    console.log('🌐 创建3D空间音频应用...');

    const spatialAudioNodes = [
      'SpatialAudioNode',
      'AudioOcclusionNode',
      'AudioDopplerNode'
    ];

    spatialAudioNodes.forEach(nodeType => {
      if (this.integrationManager.isNodeIntegrated(nodeType)) {
        const config = this.integrationManager.getNodeConfig(nodeType);
        console.log(`✅ ${config?.name}: ${config?.description}`);
        
        if (config?.uiConfig?.hasDataVisualization) {
          console.log(`📊 ${nodeType} 支持3D音频可视化`);
        }
      }
    });
  }
}

/**
 * 高级物理系统示例
 */
class AdvancedPhysicsSystemExample {
  private integrationManager: Batch02AdvancedSystemsIntegrationManager;

  constructor(integrationManager: Batch02AdvancedSystemsIntegrationManager) {
    this.integrationManager = integrationManager;
  }

  /**
   * 创建软体物理模拟应用
   */
  createSoftBodyPhysicsApp(): void {
    console.log('🧊 创建软体物理模拟应用...');

    const softBodyNodes = [
      'SoftBodyPhysicsNode',
      'ClothSimulationNode',
      'RopeSimulationNode'
    ];

    softBodyNodes.forEach(nodeType => {
      if (this.integrationManager.isNodeIntegrated(nodeType)) {
        const config = this.integrationManager.getNodeConfig(nodeType);
        console.log(`✅ ${config?.name}: ${config?.description}`);
      }
    });
  }

  /**
   * 创建流体模拟应用
   */
  createFluidSimulationApp(): void {
    console.log('💧 创建流体模拟应用...');

    const fluidConfig = this.integrationManager.getNodeConfig('FluidSimulationNode');
    if (fluidConfig) {
      console.log(`✅ 流体模拟: ${fluidConfig.name}`);
      console.log(`📝 描述: ${fluidConfig.description}`);
      
      if (fluidConfig.uiConfig?.hasDataVisualization) {
        console.log('📊 支持流体可视化');
      }
      
      if (fluidConfig.uiConfig?.hasParameterEditor) {
        console.log('⚙️ 支持参数编辑');
      }
    }
  }
}

/**
 * 主示例函数
 */
export async function runBatch02AdvancedSystemsExample(): Promise<void> {
  console.log('🚀 开始批次0.2高级系统节点示例...');

  // 创建模拟的节点编辑器
  const mockNodeEditor = {
    addNodeToPalette: (nodeType: string, config: any) => {
      console.log(`📝 添加节点到面板: ${nodeType}`);
    },
    addNodeCategory: (category: string, info: any) => {
      console.log(`📁 添加节点分类: ${category} - ${info.displayName}`);
    }
  } as any;

  try {
    // 创建集成管理器
    const integrationManager = await createBatch02AdvancedSystemsIntegration(mockNodeEditor);
    
    // 显示集成状态
    const status = integrationManager.getIntegrationStatus();
    console.log('📊 集成状态:', status);
    
    // 显示节点分类
    const categories = integrationManager.getNodeCategories();
    console.log('📁 节点分类:', Object.keys(categories));
    
    // 运行各个系统示例
    console.log('\n=== 高级输入系统示例 ===');
    const inputExample = new AdvancedInputSystemExample(integrationManager);
    inputExample.createMultiTouchGestureApp();
    inputExample.createSensorMonitoringApp();
    inputExample.createVRARInteractionApp();
    
    console.log('\n=== 高级动画系统示例 ===');
    const animationExample = new AdvancedAnimationSystemExample(integrationManager);
    animationExample.createAnimationStateMachineApp();
    animationExample.createIKSystemApp();
    
    console.log('\n=== 高级音频系统示例 ===');
    const audioExample = new AdvancedAudioSystemExample(integrationManager);
    audioExample.createAudioMixerApp();
    audioExample.create3DSpatialAudioApp();
    
    console.log('\n=== 高级物理系统示例 ===');
    const physicsExample = new AdvancedPhysicsSystemExample(integrationManager);
    physicsExample.createSoftBodyPhysicsApp();
    physicsExample.createFluidSimulationApp();
    
    console.log('\n✅ 批次0.2高级系统节点示例完成');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runBatch02AdvancedSystemsExample();
}
