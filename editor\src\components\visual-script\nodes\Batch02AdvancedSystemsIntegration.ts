/**
 * 批次0.2高级系统节点编辑器集成
 * 负责将批次0.2的68个高级系统节点集成到编辑器界面中
 * 包括：高级输入系统(25个) + 动画系统扩展(15个) + 音频系统扩展(13个) + 物理系统扩展(15个)
 */
import { NodeEditor } from '../NodeEditor';
import { NodeCategory } from '../../../types/NodeTypes';

/**
 * 批次0.2高级系统节点配置接口
 */
interface Batch02AdvancedNodeConfig {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  nodeClass: any;
  uiConfig?: {
    hasCustomPanel?: boolean;
    panelComponent?: string;
    hasDataVisualization?: boolean;
    hasParameterEditor?: boolean;
  };
}

/**
 * 节点分类映射
 */
const BATCH02_ADVANCED_CATEGORY_MAP = {
  'Input/Advanced': {
    displayName: '高级输入',
    icon: 'input',
    color: '#2196F3',
    description: '高级输入设备和传感器节点'
  },
  'Input/Sensors': {
    displayName: '传感器输入',
    icon: 'sensors',
    color: '#00BCD4',
    description: '各种传感器输入节点'
  },
  'Input/VRAR': {
    displayName: 'VR/AR输入',
    icon: 'vr_box',
    color: '#9C27B0',
    description: 'VR/AR设备输入节点'
  },
  'Animation/Advanced': {
    displayName: '高级动画',
    icon: 'animation',
    color: '#FF5722',
    description: '高级动画系统节点'
  },
  'Animation/StateMachine': {
    displayName: '动画状态机',
    icon: 'account_tree',
    color: '#E91E63',
    description: '动画状态机和混合树节点'
  },
  'Audio/Advanced': {
    displayName: '高级音频',
    icon: 'audiotrack',
    color: '#4CAF50',
    description: '高级音频处理节点'
  },
  'Audio/Effects': {
    displayName: '音频效果',
    icon: 'equalizer',
    color: '#8BC34A',
    description: '音频效果和处理节点'
  },
  'Physics/Advanced': {
    displayName: '高级物理',
    icon: 'physics',
    color: '#795548',
    description: '高级物理模拟节点'
  },
  'Physics/Simulation': {
    displayName: '物理模拟',
    icon: 'waves',
    color: '#607D8B',
    description: '流体、软体等物理模拟节点'
  }
};

/**
 * 批次0.2高级系统节点编辑器集成类
 */
export class Batch02AdvancedSystemsIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, Batch02AdvancedNodeConfig> = new Map();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
  }

  /**
   * 集成所有批次0.2高级系统节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成批次0.2高级系统节点到编辑器...');

    // 高级输入系统节点 (25个)
    this.integrateAdvancedInputNodes();

    // 动画系统扩展节点 (15个)
    this.integrateAnimationExtensionNodes();

    // 音频系统扩展节点 (13个)
    this.integrateAudioExtensionNodes();

    // 物理系统扩展节点 (15个)
    this.integratePhysicsExtensionNodes();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('批次0.2高级系统节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
    console.log('包含：高级输入(25) + 动画扩展(15) + 音频扩展(13) + 物理扩展(15) = 68个节点');
  }

  /**
   * 集成高级输入系统节点 (25个)
   */
  private integrateAdvancedInputNodes(): void {
    console.log('集成高级输入系统节点...');

    // 多点触控和手势节点 (10个)
    const touchGestureNodes = [
      'MultiTouchNode', 'GestureRecognitionNode', 'TouchPressureNode', 'TouchVelocityNode',
      'PinchGestureNode', 'SwipeGestureNode', 'RotationGestureNode', 'TapGestureNode',
      'LongPressGestureNode', 'CustomGestureNode'
    ];

    // 传感器输入节点 (10个)
    const sensorNodes = [
      'AccelerometerNode', 'GyroscopeNode', 'CompassNode', 'ProximityNode',
      'LightSensorNode', 'PressureSensorNode', 'TemperatureSensorNode', 'HumiditySensorNode',
      'MotionSensorNode', 'OrientationSensorNode'
    ];

    // VR/AR输入节点 (5个)
    const vrArInputNodes = [
      'VRControllerInputNode', 'VRHeadsetTrackingNode', 'ARTouchInputNode',
      'EyeTrackingInputNode', 'HandTrackingInputNode'
    ];

    // 注册多点触控和手势节点
    touchGestureNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Input/Advanced',
        icon: 'touch_app',
        color: '#2196F3',
        tags: ['input', 'touch', 'gesture', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasCustomPanel: true,
          hasDataVisualization: true,
          hasParameterEditor: true
        }
      });
    });

    // 注册传感器输入节点
    sensorNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Input/Sensors',
        icon: 'sensors',
        color: '#00BCD4',
        tags: ['input', 'sensor', 'hardware', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasDataVisualization: true,
          hasParameterEditor: true
        }
      });
    });

    // 注册VR/AR输入节点
    vrArInputNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Input/VRAR',
        icon: 'vr_box',
        color: '#9C27B0',
        tags: ['input', 'vr', 'ar', 'tracking', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasCustomPanel: true,
          hasDataVisualization: true
        }
      });
    });

    console.log('高级输入系统节点集成完成 - 25个节点');
  }

  /**
   * 集成动画系统扩展节点 (15个)
   */
  private integrateAnimationExtensionNodes(): void {
    console.log('集成动画系统扩展节点...');

    // 动画状态机节点 (8个)
    const stateMachineNodes = [
      'AnimationStateMachineNode', 'AnimationStateNode', 'AnimationTransitionNode',
      'AnimationBlendTreeNode', 'AnimationLayerNode', 'AnimationParameterNode',
      'AnimationConditionNode', 'AnimationEventNode'
    ];

    // IK和高级动画节点 (7个)
    const advancedAnimationNodes = [
      'IKSystemNode', 'IKChainNode', 'IKConstraintNode', 'AnimationRetargetingNode',
      'AnimationCompressionNode', 'AnimationOptimizationNode', 'KeyframeEditorNode'
    ];

    // 注册动画状态机节点
    stateMachineNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Animation/StateMachine',
        icon: 'account_tree',
        color: '#E91E63',
        tags: ['animation', 'state', 'machine', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasCustomPanel: true,
          panelComponent: 'AnimationStateMachinePanel',
          hasParameterEditor: true
        }
      });
    });

    // 注册高级动画节点
    advancedAnimationNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Animation/Advanced',
        icon: 'animation',
        color: '#FF5722',
        tags: ['animation', 'advanced', 'ik', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasParameterEditor: true,
          hasDataVisualization: nodeType.includes('IK')
        }
      });
    });

    console.log('动画系统扩展节点集成完成 - 15个节点');
  }

  /**
   * 集成音频系统扩展节点 (13个)
   */
  private integrateAudioExtensionNodes(): void {
    console.log('集成音频系统扩展节点...');

    // 音频混合和处理节点 (8个)
    const audioProcessingNodes = [
      'AudioMixerNode', 'AudioEffectChainNode', 'AudioReverbNode', 'AudioEQNode',
      'AudioCompressorNode', 'AudioDelayNode', 'AudioChorusNode', 'AudioDistortionNode'
    ];

    // 3D音频和高级功能节点 (5个)
    const advanced3DAudioNodes = [
      'SpatialAudioNode', 'AudioOcclusionNode', 'AudioDopplerNode',
      'AudioOptimizationNode', 'AudioAnalyzerNode'
    ];

    // 注册音频处理节点
    audioProcessingNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Audio/Effects',
        icon: 'equalizer',
        color: '#8BC34A',
        tags: ['audio', 'effects', 'processing', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasCustomPanel: nodeType === 'AudioMixerNode',
          panelComponent: nodeType === 'AudioMixerNode' ? 'AudioMixerPanel' : undefined,
          hasParameterEditor: true
        }
      });
    });

    // 注册3D音频节点
    advanced3DAudioNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Audio/Advanced',
        icon: 'audiotrack',
        color: '#4CAF50',
        tags: ['audio', '3d', 'spatial', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasDataVisualization: true,
          hasParameterEditor: true
        }
      });
    });

    console.log('音频系统扩展节点集成完成 - 13个节点');
  }

  /**
   * 集成物理系统扩展节点 (15个)
   */
  private integratePhysicsExtensionNodes(): void {
    console.log('集成物理系统扩展节点...');

    // 软体和流体物理节点 (8个)
    const softFluidNodes = [
      'SoftBodyPhysicsNode', 'FluidSimulationNode', 'ClothSimulationNode', 'RopeSimulationNode',
      'ParticlePhysicsNode', 'DestructionNode', 'PhysicsConstraintNode', 'PhysicsJointNode'
    ];

    // 物理优化和监控节点 (7个)
    const physicsOptimizationNodes = [
      'PhysicsOptimizationNode', 'PhysicsLODNode', 'PhysicsPerformanceMonitorNode',
      'PhysicsDebugNode', 'PhysicsProfilerNode', 'PhysicsMotorNode', 'PhysicsMaterialNode'
    ];

    // 注册软体和流体物理节点
    softFluidNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Physics/Simulation',
        icon: 'waves',
        color: '#607D8B',
        tags: ['physics', 'simulation', 'soft', 'fluid', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasDataVisualization: true,
          hasParameterEditor: true
        }
      });
    });

    // 注册物理优化节点
    physicsOptimizationNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Physics/Advanced',
        icon: 'physics',
        color: '#795548',
        tags: ['physics', 'optimization', 'performance', 'batch02'],
        nodeClass: null,
        uiConfig: {
          hasParameterEditor: true,
          hasDataVisualization: nodeType.includes('Monitor') || nodeType.includes('Debug')
        }
      });
    });

    console.log('物理系统扩展节点集成完成 - 15个节点');
  }

  /**
   * 获取节点显示名称
   */
  private getNodeDisplayName(nodeType: string): string {
    const nameMap: { [key: string]: string } = {
      // 高级输入节点
      'MultiTouchNode': '多点触控',
      'GestureRecognitionNode': '手势识别',
      'TouchPressureNode': '触控压力',
      'TouchVelocityNode': '触控速度',
      'PinchGestureNode': '捏合手势',
      'SwipeGestureNode': '滑动手势',
      'RotationGestureNode': '旋转手势',
      'TapGestureNode': '点击手势',
      'LongPressGestureNode': '长按手势',
      'CustomGestureNode': '自定义手势',
      
      // 传感器节点
      'AccelerometerNode': '加速度计',
      'GyroscopeNode': '陀螺仪',
      'CompassNode': '指南针',
      'ProximityNode': '距离传感器',
      'LightSensorNode': '光线传感器',
      'PressureSensorNode': '压力传感器',
      'TemperatureSensorNode': '温度传感器',
      'HumiditySensorNode': '湿度传感器',
      'MotionSensorNode': '运动传感器',
      'OrientationSensorNode': '方向传感器',
      
      // VR/AR输入节点
      'VRControllerInputNode': 'VR控制器输入',
      'VRHeadsetTrackingNode': 'VR头显追踪',
      'ARTouchInputNode': 'AR触控输入',
      'EyeTrackingInputNode': '眼动追踪输入',
      'HandTrackingInputNode': '手部追踪输入',
      
      // 动画状态机节点
      'AnimationStateMachineNode': '动画状态机',
      'AnimationStateNode': '动画状态',
      'AnimationTransitionNode': '动画过渡',
      'AnimationBlendTreeNode': '动画混合树',
      'AnimationLayerNode': '动画层',
      'AnimationParameterNode': '动画参数',
      'AnimationConditionNode': '动画条件',
      'AnimationEventNode': '动画事件',
      
      // 高级动画节点
      'IKSystemNode': 'IK系统',
      'IKChainNode': 'IK链',
      'IKConstraintNode': 'IK约束',
      'AnimationRetargetingNode': '动画重定向',
      'AnimationCompressionNode': '动画压缩',
      'AnimationOptimizationNode': '动画优化',
      'KeyframeEditorNode': '关键帧编辑器',
      
      // 音频处理节点
      'AudioMixerNode': '音频混合器',
      'AudioEffectChainNode': '音频效果链',
      'AudioReverbNode': '音频混响',
      'AudioEQNode': '音频均衡器',
      'AudioCompressorNode': '音频压缩器',
      'AudioDelayNode': '音频延迟',
      'AudioChorusNode': '音频合唱',
      'AudioDistortionNode': '音频失真',
      
      // 3D音频节点
      'SpatialAudioNode': '空间音频',
      'AudioOcclusionNode': '音频遮挡',
      'AudioDopplerNode': '音频多普勒',
      'AudioOptimizationNode': '音频优化',
      'AudioAnalyzerNode': '音频分析器',
      
      // 软体流体物理节点
      'SoftBodyPhysicsNode': '软体物理',
      'FluidSimulationNode': '流体模拟',
      'ClothSimulationNode': '布料模拟',
      'RopeSimulationNode': '绳索模拟',
      'ParticlePhysicsNode': '粒子物理',
      'DestructionNode': '破坏效果',
      'PhysicsConstraintNode': '物理约束',
      'PhysicsJointNode': '物理关节',
      
      // 物理优化节点
      'PhysicsOptimizationNode': '物理优化',
      'PhysicsLODNode': '物理LOD',
      'PhysicsPerformanceMonitorNode': '物理性能监控',
      'PhysicsDebugNode': '物理调试',
      'PhysicsProfilerNode': '物理分析器',
      'PhysicsMotorNode': '物理马达',
      'PhysicsMaterialNode': '物理材质'
    };

    return nameMap[nodeType] || nodeType.replace('Node', '');
  }

  /**
   * 获取节点描述
   */
  private getNodeDescription(nodeType: string): string {
    const descMap: { [key: string]: string } = {
      // 高级输入节点
      'MultiTouchNode': '处理多点触控输入，支持多指手势识别',
      'GestureRecognitionNode': '识别和处理各种手势操作',
      'TouchPressureNode': '检测触控压力变化，支持压感操作',
      'TouchVelocityNode': '计算触控速度和加速度',
      'PinchGestureNode': '识别捏合手势，用于缩放操作',
      'SwipeGestureNode': '识别滑动手势，支持方向检测',
      'RotationGestureNode': '识别旋转手势，计算旋转角度',
      'TapGestureNode': '识别点击手势，支持单击和双击',
      'LongPressGestureNode': '识别长按手势，可配置时长',
      'CustomGestureNode': '自定义手势识别，支持手势库管理',

      // 传感器节点
      'AccelerometerNode': '读取设备加速度数据，检测运动状态',
      'GyroscopeNode': '读取陀螺仪数据，检测设备旋转',
      'CompassNode': '读取指南针数据，获取设备方向',
      'ProximityNode': '检测物体接近距离，支持阈值设置',
      'LightSensorNode': '检测环境光照强度，自动调节亮度',
      'PressureSensorNode': '检测大气压力变化，用于高度测量',
      'TemperatureSensorNode': '检测环境温度，支持温度监控',
      'HumiditySensorNode': '检测环境湿度，用于环境监测',
      'MotionSensorNode': '综合运动传感器，检测设备运动状态',
      'OrientationSensorNode': '检测设备方向，提供三轴角度数据',

      // VR/AR输入节点
      'VRControllerInputNode': 'VR控制器输入处理，支持按键和位置追踪',
      'VRHeadsetTrackingNode': 'VR头显位置追踪，提供6DOF数据',
      'ARTouchInputNode': 'AR环境下的触控输入处理',
      'EyeTrackingInputNode': '眼动追踪输入，支持注视点检测',
      'HandTrackingInputNode': '手部追踪输入，识别手部姿态',

      // 动画状态机节点
      'AnimationStateMachineNode': '动画状态机控制器，管理动画状态转换',
      'AnimationStateNode': '动画状态节点，定义单个动画状态',
      'AnimationTransitionNode': '动画过渡节点，控制状态间的转换',
      'AnimationBlendTreeNode': '动画混合树，实现复杂动画混合',
      'AnimationLayerNode': '动画层节点，支持多层动画叠加',
      'AnimationParameterNode': '动画参数节点，控制动画播放参数',
      'AnimationConditionNode': '动画条件节点，定义状态转换条件',
      'AnimationEventNode': '动画事件节点，处理动画事件回调',

      // 高级动画节点
      'IKSystemNode': 'IK系统控制器，管理反向动力学计算',
      'IKChainNode': 'IK链节点，定义骨骼链约束',
      'IKConstraintNode': 'IK约束节点，设置IK约束条件',
      'AnimationRetargetingNode': '动画重定向，适配不同骨架结构',
      'AnimationCompressionNode': '动画压缩，减少动画数据大小',
      'AnimationOptimizationNode': '动画优化，提升动画播放性能',
      'KeyframeEditorNode': '关键帧编辑器，可视化编辑动画关键帧',

      // 音频处理节点
      'AudioMixerNode': '音频混合器，混合多路音频信号',
      'AudioEffectChainNode': '音频效果链，串联多个音频效果',
      'AudioReverbNode': '音频混响效果，模拟空间声学',
      'AudioEQNode': '音频均衡器，调节音频频率响应',
      'AudioCompressorNode': '音频压缩器，控制音频动态范围',
      'AudioDelayNode': '音频延迟效果，创建回声效果',
      'AudioChorusNode': '音频合唱效果，增加音频厚度',
      'AudioDistortionNode': '音频失真效果，创建失真音色',

      // 3D音频节点
      'SpatialAudioNode': '空间音频处理，实现3D音效定位',
      'AudioOcclusionNode': '音频遮挡计算，模拟声音遮挡效果',
      'AudioDopplerNode': '音频多普勒效应，模拟运动音源',
      'AudioOptimizationNode': '音频性能优化，减少音频处理开销',
      'AudioAnalyzerNode': '音频分析器，实时分析音频特征',

      // 软体流体物理节点
      'SoftBodyPhysicsNode': '软体物理模拟，模拟可变形物体',
      'FluidSimulationNode': '流体模拟系统，模拟液体和气体',
      'ClothSimulationNode': '布料模拟，模拟织物物理特性',
      'RopeSimulationNode': '绳索模拟，模拟绳索和链条',
      'ParticlePhysicsNode': '粒子物理系统，模拟粒子运动',
      'DestructionNode': '破坏效果模拟，实现物体破碎效果',
      'PhysicsConstraintNode': '物理约束系统，限制物体运动',
      'PhysicsJointNode': '物理关节连接，连接多个物理对象',

      // 物理优化节点
      'PhysicsOptimizationNode': '物理性能优化，提升物理计算效率',
      'PhysicsLODNode': '物理LOD系统，根据距离调整物理精度',
      'PhysicsPerformanceMonitorNode': '物理性能监控，监测物理系统性能',
      'PhysicsDebugNode': '物理调试工具，可视化物理计算过程',
      'PhysicsProfilerNode': '物理性能分析器，分析物理性能瓶颈',
      'PhysicsMotorNode': '物理马达驱动，提供物理驱动力',
      'PhysicsMaterialNode': '物理材质定义，设置物理材质属性'
    };

    return descMap[nodeType] || `${nodeType}节点的功能描述`;
  }

  /**
   * 注册节点
   */
  private registerNode(config: Batch02AdvancedNodeConfig): void {
    this.registeredNodes.set(config.type, config);

    // 添加到分类
    const categoryNodes = this.categoryNodes.get(config.category) || [];
    categoryNodes.push(config.type);
    this.categoryNodes.set(config.category, categoryNodes);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 将节点添加到编辑器的节点面板
    for (const [nodeType, nodeConfig] of this.registeredNodes.entries()) {
      this.nodeEditor.addNodeToPalette(nodeType, nodeConfig);
    }

    console.log('批次0.2高级系统节点面板设置完成: 68个节点已添加到编辑器');
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 为编辑器添加新的节点分类
    for (const [category, categoryInfo] of Object.entries(BATCH02_ADVANCED_CATEGORY_MAP)) {
      this.nodeEditor.addNodeCategory(category, {
        displayName: categoryInfo.displayName,
        icon: categoryInfo.icon,
        color: categoryInfo.color,
        description: categoryInfo.description,
        nodes: this.categoryNodes.get(category) || []
      });
    }

    console.log('批次0.2高级系统节点分类设置完成: 9个分类已添加到编辑器');
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, Batch02AdvancedNodeConfig> {
    return this.registeredNodes;
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, string[]> {
    return this.categoryNodes;
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取节点配置
   */
  public getNodeConfig(nodeType: string): Batch02AdvancedNodeConfig | undefined {
    return this.registeredNodes.get(nodeType);
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): any {
    const stats = {
      totalNodes: this.registeredNodes.size,
      categories: this.categoryNodes.size,
      nodesByCategory: {} as any
    };

    for (const [category, nodes] of this.categoryNodes.entries()) {
      stats.nodesByCategory[category] = nodes.length;
    }

    return stats;
  }
}

// 导出集成函数
export function integrateBatch02AdvancedSystemsNodes(nodeEditor: NodeEditor): Batch02AdvancedSystemsIntegration {
  return new Batch02AdvancedSystemsIntegration(nodeEditor);
}
