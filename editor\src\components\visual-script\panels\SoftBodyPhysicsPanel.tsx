/**
 * 软体物理面板组件
 * 为SoftBodyPhysicsNode提供软体物理参数配置和可视化
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Slider,
  Switch,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Tooltip,
  Select,
  InputNumber,
  Divider,
  Alert,
  Progress,
  Tabs
} from 'antd';
import {
  ExperimentOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  BarChartOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 软体物理参数接口
 */
interface SoftBodyParameters {
  mass: number;
  stiffness: number;
  damping: number;
  pressure: number;
  volume: number;
  iterations: number;
  collisionMargin: number;
  friction: number;
  restitution: number;
  linearDamping: number;
  angularDamping: number;
}

/**
 * 软体约束接口
 */
interface SoftBodyConstraint {
  id: string;
  type: 'spring' | 'distance' | 'volume' | 'anchor';
  strength: number;
  enabled: boolean;
  parameters: { [key: string]: any };
}

/**
 * 软体物理状态接口
 */
interface SoftBodyState {
  isSimulating: boolean;
  currentStep: number;
  totalEnergy: number;
  kineticEnergy: number;
  potentialEnergy: number;
  deformation: number;
  stress: number;
  strain: number;
}

/**
 * 软体物理面板属性
 */
interface SoftBodyPhysicsPanelProps {
  nodeId?: string;
  onParametersChange?: (parameters: SoftBodyParameters) => void;
  onConstraintsChange?: (constraints: SoftBodyConstraint[]) => void;
  readonly?: boolean;
}

/**
 * 软体物理面板组件
 */
export const SoftBodyPhysicsPanel: React.FC<SoftBodyPhysicsPanelProps> = ({
  nodeId,
  onParametersChange,
  onConstraintsChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  
  // 状态管理
  const [parameters, setParameters] = useState<SoftBodyParameters>({
    mass: 1.0,
    stiffness: 0.8,
    damping: 0.1,
    pressure: 0.5,
    volume: 1.0,
    iterations: 10,
    collisionMargin: 0.01,
    friction: 0.5,
    restitution: 0.3,
    linearDamping: 0.1,
    angularDamping: 0.1
  });
  
  const [constraints, setConstraints] = useState<SoftBodyConstraint[]>([
    {
      id: 'spring1',
      type: 'spring',
      strength: 0.8,
      enabled: true,
      parameters: { restLength: 1.0, maxForce: 100 }
    },
    {
      id: 'volume1',
      type: 'volume',
      strength: 0.6,
      enabled: true,
      parameters: { targetVolume: 1.0, compressionLimit: 0.1 }
    }
  ]);
  
  const [state, setState] = useState<SoftBodyState>({
    isSimulating: false,
    currentStep: 0,
    totalEnergy: 0,
    kineticEnergy: 0,
    potentialEnergy: 0,
    deformation: 0,
    stress: 0,
    strain: 0
  });
  
  // UI状态
  const [showVisualization, setShowVisualization] = useState(true);
  const [showForces, setShowForces] = useState(true);
  const [showConstraints, setShowConstraints] = useState(true);
  const [visualizationMode, setVisualizationMode] = useState<'mesh' | 'wireframe' | 'stress'>('mesh');
  const [selectedConstraint, setSelectedConstraint] = useState<string>('');

  /**
   * 更新参数
   */
  const updateParameter = useCallback((parameter: string, value: number) => {
    const newParameters = { ...parameters, [parameter]: value };
    setParameters(newParameters);
    onParametersChange?.(newParameters);
  }, [parameters, onParametersChange]);

  /**
   * 更新约束
   */
  const updateConstraint = useCallback((constraintId: string, property: string, value: any) => {
    const newConstraints = constraints.map(constraint => 
      constraint.id === constraintId 
        ? { ...constraint, [property]: value }
        : constraint
    );
    setConstraints(newConstraints);
    onConstraintsChange?.(newConstraints);
  }, [constraints, onConstraintsChange]);

  /**
   * 模拟控制
   */
  const startSimulation = useCallback(() => {
    setState(prev => ({ ...prev, isSimulating: true }));
    
    // 模拟物理计算
    const simulate = () => {
      setState(prev => ({
        ...prev,
        currentStep: prev.currentStep + 1,
        totalEnergy: Math.random() * 100,
        kineticEnergy: Math.random() * 50,
        potentialEnergy: Math.random() * 50,
        deformation: Math.random() * 0.5,
        stress: Math.random() * 10,
        strain: Math.random() * 0.3
      }));
      
      if (state.isSimulating) {
        animationRef.current = requestAnimationFrame(simulate);
      }
    };
    
    simulate();
  }, [state.isSimulating]);

  const pauseSimulation = useCallback(() => {
    setState(prev => ({ ...prev, isSimulating: false }));
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  }, []);

  const resetSimulation = useCallback(() => {
    setState({
      isSimulating: false,
      currentStep: 0,
      totalEnergy: 0,
      kineticEnergy: 0,
      potentialEnergy: 0,
      deformation: 0,
      stress: 0,
      strain: 0
    });
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  }, []);

  /**
   * 绘制软体可视化
   */
  const drawSoftBodyVisualization = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const size = 100;

    // 根据可视化模式绘制
    switch (visualizationMode) {
      case 'mesh':
        drawMeshVisualization(ctx, centerX, centerY, size);
        break;
      case 'wireframe':
        drawWireframeVisualization(ctx, centerX, centerY, size);
        break;
      case 'stress':
        drawStressVisualization(ctx, centerX, centerY, size);
        break;
    }

    // 绘制约束
    if (showConstraints) {
      drawConstraints(ctx, centerX, centerY, size);
    }

    // 绘制力向量
    if (showForces) {
      drawForceVectors(ctx, centerX, centerY, size);
    }
  }, [visualizationMode, showConstraints, showForces, state, parameters]);

  /**
   * 绘制网格可视化
   */
  const drawMeshVisualization = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, size: number) => {
    const deformation = state.deformation;
    
    // 绘制变形的软体网格
    ctx.fillStyle = `rgba(52, 196, 26, ${0.3 + deformation * 0.4})`;
    ctx.strokeStyle = '#52c41a';
    ctx.lineWidth = 2;
    
    // 创建变形的椭圆
    ctx.beginPath();
    ctx.ellipse(
      centerX, 
      centerY + deformation * 20, 
      size * (1 + deformation * 0.3), 
      size * (1 - deformation * 0.2), 
      0, 0, 2 * Math.PI
    );
    ctx.fill();
    ctx.stroke();
    
    // 绘制网格线
    ctx.strokeStyle = '#389e0d';
    ctx.lineWidth = 1;
    for (let i = -3; i <= 3; i++) {
      ctx.beginPath();
      ctx.moveTo(centerX - size, centerY + i * 20);
      ctx.lineTo(centerX + size, centerY + i * 20);
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(centerX + i * 20, centerY - size);
      ctx.lineTo(centerX + i * 20, centerY + size);
      ctx.stroke();
    }
  };

  /**
   * 绘制线框可视化
   */
  const drawWireframeVisualization = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, size: number) => {
    ctx.strokeStyle = '#1890ff';
    ctx.lineWidth = 1;
    
    // 绘制线框结构
    const points = [];
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * 2 * Math.PI;
      const radius = size + Math.sin(state.currentStep * 0.1 + i) * state.deformation * 20;
      points.push({
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * radius
      });
    }
    
    // 连接所有点
    for (let i = 0; i < points.length; i++) {
      const nextIndex = (i + 1) % points.length;
      ctx.beginPath();
      ctx.moveTo(points[i].x, points[i].y);
      ctx.lineTo(points[nextIndex].x, points[nextIndex].y);
      ctx.stroke();
      
      // 绘制到中心的连线
      ctx.beginPath();
      ctx.moveTo(points[i].x, points[i].y);
      ctx.lineTo(centerX, centerY);
      ctx.stroke();
    }
  };

  /**
   * 绘制应力可视化
   */
  const drawStressVisualization = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, size: number) => {
    const stress = state.stress;
    const maxStress = 10;
    
    // 根据应力值选择颜色
    const stressRatio = Math.min(stress / maxStress, 1);
    const red = Math.floor(255 * stressRatio);
    const green = Math.floor(255 * (1 - stressRatio));
    
    ctx.fillStyle = `rgb(${red}, ${green}, 0)`;
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;
    
    // 绘制应力分布
    for (let x = -size; x <= size; x += 20) {
      for (let y = -size; y <= size; y += 20) {
        const distance = Math.sqrt(x * x + y * y);
        if (distance <= size) {
          const localStress = stress * (1 - distance / size);
          const localRed = Math.floor(255 * localStress / maxStress);
          const localGreen = Math.floor(255 * (1 - localStress / maxStress));
          
          ctx.fillStyle = `rgb(${localRed}, ${localGreen}, 0)`;
          ctx.fillRect(centerX + x - 5, centerY + y - 5, 10, 10);
        }
      }
    }
  };

  /**
   * 绘制约束
   */
  const drawConstraints = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, size: number) => {
    constraints.forEach((constraint, index) => {
      if (!constraint.enabled) return;
      
      ctx.strokeStyle = constraint.id === selectedConstraint ? '#ff4d4f' : '#722ed1';
      ctx.lineWidth = constraint.id === selectedConstraint ? 3 : 2;
      ctx.setLineDash(constraint.type === 'spring' ? [5, 5] : []);
      
      const angle = (index / constraints.length) * 2 * Math.PI;
      const x = centerX + Math.cos(angle) * size * 0.8;
      const y = centerY + Math.sin(angle) * size * 0.8;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.stroke();
      
      // 绘制约束点
      ctx.fillStyle = constraint.id === selectedConstraint ? '#ff4d4f' : '#722ed1';
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.setLineDash([]);
    });
  };

  /**
   * 绘制力向量
   */
  const drawForceVectors = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, size: number) => {
    ctx.strokeStyle = '#fa8c16';
    ctx.lineWidth = 2;
    
    // 绘制重力
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(centerX, centerY + 30);
    ctx.stroke();
    
    // 箭头
    ctx.beginPath();
    ctx.moveTo(centerX, centerY + 30);
    ctx.lineTo(centerX - 5, centerY + 25);
    ctx.moveTo(centerX, centerY + 30);
    ctx.lineTo(centerX + 5, centerY + 25);
    ctx.stroke();
    
    // 标签
    ctx.fillStyle = '#fa8c16';
    ctx.font = '12px Arial';
    ctx.fillText('重力', centerX + 10, centerY + 35);
  };

  /**
   * 渲染参数控制
   */
  const renderParameterControls = () => (
    <Card title="物理参数" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>质量</Text>
            <Slider
              min={0.1}
              max={10}
              step={0.1}
              value={parameters.mass}
              onChange={(value) => updateParameter('mass', value)}
              disabled={readonly}
            />
            <Text type="secondary">{parameters.mass.toFixed(1)} kg</Text>
          </Col>
          <Col span={12}>
            <Text strong>刚度</Text>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={parameters.stiffness}
              onChange={(value) => updateParameter('stiffness', value)}
              disabled={readonly}
            />
            <Text type="secondary">{(parameters.stiffness * 100).toFixed(0)}%</Text>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Text strong>阻尼</Text>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={parameters.damping}
              onChange={(value) => updateParameter('damping', value)}
              disabled={readonly}
            />
            <Text type="secondary">{(parameters.damping * 100).toFixed(0)}%</Text>
          </Col>
          <Col span={12}>
            <Text strong>压力</Text>
            <Slider
              min={0}
              max={2}
              step={0.01}
              value={parameters.pressure}
              onChange={(value) => updateParameter('pressure', value)}
              disabled={readonly}
            />
            <Text type="secondary">{parameters.pressure.toFixed(2)} atm</Text>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Text strong>摩擦力</Text>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={parameters.friction}
              onChange={(value) => updateParameter('friction', value)}
              disabled={readonly}
            />
          </Col>
          <Col span={12}>
            <Text strong>弹性</Text>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={parameters.restitution}
              onChange={(value) => updateParameter('restitution', value)}
              disabled={readonly}
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Text strong>迭代次数</Text>
            <InputNumber
              min={1}
              max={50}
              value={parameters.iterations}
              onChange={(value) => updateParameter('iterations', value || 10)}
              disabled={readonly}
              size="small"
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={12}>
            <Text strong>碰撞边距</Text>
            <InputNumber
              min={0.001}
              max={0.1}
              step={0.001}
              value={parameters.collisionMargin}
              onChange={(value) => updateParameter('collisionMargin', value || 0.01)}
              disabled={readonly}
              size="small"
              style={{ width: '100%' }}
            />
          </Col>
        </Row>
      </Space>
    </Card>
  );

  /**
   * 渲染约束控制
   */
  const renderConstraintControls = () => (
    <Card title="约束设置" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        {constraints.map(constraint => (
          <Card 
            key={constraint.id} 
            size="small" 
            style={{ 
              marginBottom: 8,
              border: constraint.id === selectedConstraint ? '2px solid #1890ff' : undefined
            }}
            onClick={() => setSelectedConstraint(constraint.id)}
          >
            <Row align="middle" justify="space-between">
              <Col>
                <Space>
                  <Tag color="blue">{constraint.type}</Tag>
                  <Text>强度: {(constraint.strength * 100).toFixed(0)}%</Text>
                </Space>
              </Col>
              <Col>
                <Switch
                  size="small"
                  checked={constraint.enabled}
                  onChange={(checked) => updateConstraint(constraint.id, 'enabled', checked)}
                  disabled={readonly}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 8 }}>
              <Slider
                min={0}
                max={1}
                step={0.01}
                value={constraint.strength}
                onChange={(value) => updateConstraint(constraint.id, 'strength', value)}
                disabled={readonly}
                size="small"
              />
            </div>
          </Card>
        ))}
      </Space>
    </Card>
  );

  /**
   * 渲染状态监控
   */
  const renderStateMonitoring = () => (
    <Card title="状态监控" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>总能量</Text>
            <Progress 
              percent={Math.min(state.totalEnergy, 100)} 
              size="small"
              strokeColor="#52c41a"
            />
            <Text type="secondary">{state.totalEnergy.toFixed(2)} J</Text>
          </Col>
          <Col span={12}>
            <Text strong>变形量</Text>
            <Progress 
              percent={state.deformation * 100} 
              size="small"
              strokeColor="#faad14"
            />
            <Text type="secondary">{(state.deformation * 100).toFixed(1)}%</Text>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Text strong>应力</Text>
            <Progress 
              percent={Math.min(state.stress * 10, 100)} 
              size="small"
              strokeColor="#ff4d4f"
            />
            <Text type="secondary">{state.stress.toFixed(2)} MPa</Text>
          </Col>
          <Col span={12}>
            <Text strong>应变</Text>
            <Progress 
              percent={state.strain * 100} 
              size="small"
              strokeColor="#722ed1"
            />
            <Text type="secondary">{(state.strain * 100).toFixed(1)}%</Text>
          </Col>
        </Row>

        <Divider />
        
        <Row align="middle" justify="space-between">
          <Col>
            <Text strong>模拟步数: {state.currentStep}</Text>
          </Col>
          <Col>
            <Tag color={state.isSimulating ? 'green' : 'default'}>
              {state.isSimulating ? '运行中' : '已停止'}
            </Tag>
          </Col>
        </Row>
      </Space>
    </Card>
  );

  // 绘制可视化
  useEffect(() => {
    if (showVisualization) {
      drawSoftBodyVisualization();
    }
  }, [showVisualization, drawSoftBodyVisualization, state]);

  return (
    <div className="soft-body-physics-panel">
      <Card
        title={
          <Space>
            <ExperimentOutlined />
            <Title level={4} style={{ margin: 0 }}>
              软体物理模拟
            </Title>
          </Space>
        }
        size="small"
      >
        <Tabs defaultActiveKey="visualization" size="small">
          <TabPane tab="可视化" key="visualization">
            <Row gutter={16}>
              <Col span={16}>
                <Card title="软体可视化" size="small">
                  <Space style={{ marginBottom: 8 }}>
                    <Button
                      type={state.isSimulating ? "default" : "primary"}
                      icon={<PlayCircleOutlined />}
                      onClick={startSimulation}
                      disabled={readonly || state.isSimulating}
                      size="small"
                    >
                      开始
                    </Button>
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={pauseSimulation}
                      disabled={readonly || !state.isSimulating}
                      size="small"
                    >
                      暂停
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={resetSimulation}
                      disabled={readonly}
                      size="small"
                    >
                      重置
                    </Button>
                    
                    <Select
                      value={visualizationMode}
                      onChange={setVisualizationMode}
                      size="small"
                    >
                      <Option value="mesh">网格</Option>
                      <Option value="wireframe">线框</Option>
                      <Option value="stress">应力</Option>
                    </Select>
                    
                    <Switch
                      size="small"
                      checked={showConstraints}
                      onChange={setShowConstraints}
                      checkedChildren="约束"
                      unCheckedChildren="约束"
                    />
                    
                    <Switch
                      size="small"
                      checked={showForces}
                      onChange={setShowForces}
                      checkedChildren="力"
                      unCheckedChildren="力"
                    />
                  </Space>
                  
                  <canvas
                    ref={canvasRef}
                    width={500}
                    height={300}
                    style={{ 
                      border: '1px solid #d9d9d9',
                      backgroundColor: '#fafafa'
                    }}
                  />
                </Card>
              </Col>
              
              <Col span={8}>
                {renderStateMonitoring()}
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab="参数" key="parameters">
            {renderParameterControls()}
          </TabPane>
          
          <TabPane tab="约束" key="constraints">
            {renderConstraintControls()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default SoftBodyPhysicsPanel;
