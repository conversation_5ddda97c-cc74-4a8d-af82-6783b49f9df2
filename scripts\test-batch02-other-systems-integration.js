/**
 * 测试批次0.2其他系统集成的所有节点
 * 综合测试粒子系统、地形编辑、动作捕捉、计算机视觉节点的集成情况
 */

const fs = require('fs');
const path = require('path');

class Batch02OtherSystemsIntegrationTest {
  constructor() {
    this.results = {
      particleSystem: { success: false, nodeCount: 0, errors: [] },
      terrainEditing: { success: false, nodeCount: 0, errors: [] },
      motionCapture: { success: false, nodeCount: 0, errors: [] },
      computerVision: { success: false, nodeCount: 0, errors: [] },
      overall: { success: false, totalNodes: 0, errors: [] }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始测试批次0.2其他系统集成的所有节点...\n');

    try {
      // 测试粒子系统节点 (8个)
      await this.testParticleSystemNodes();
      
      // 测试地形编辑节点 (12个)
      await this.testTerrainEditingNodes();
      
      // 测试动作捕捉节点 (6个)
      await this.testMotionCaptureNodes();
      
      // 测试计算机视觉节点 (15个)
      await this.testComputerVisionNodes();
      
      // 测试主NodeRegistry集成
      await this.testMainRegistryIntegration();
      
      // 生成综合测试报告
      this.generateComprehensiveReport();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      this.results.overall.errors.push(`测试执行错误: ${error.message}`);
    }
  }

  /**
   * 测试粒子系统节点
   */
  async testParticleSystemNodes() {
    console.log('🎆 测试粒子系统节点集成...');
    
    try {
      const expectedNodes = [
        'ParticleSystemEditor',
        'ParticleEmitterEditor',
        'ParticlePreview',
        'ParticleLibrary',
        'ParticleExport',
        'ParticleImport',
        'ParticleForceEditor',
        'ParticleCollisionEditor'
      ];

      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      const registryContent = fs.readFileSync(registryPath, 'utf8');

      let registeredCount = 0;
      expectedNodes.forEach(nodeType => {
        if (registryContent.includes(`'${nodeType}'`)) {
          registeredCount++;
        } else {
          this.results.particleSystem.errors.push(`粒子系统节点未注册: ${nodeType}`);
        }
      });

      this.results.particleSystem.nodeCount = registeredCount;
      this.results.particleSystem.success = registeredCount === expectedNodes.length;

      console.log(`   ✅ 粒子系统节点: ${registeredCount}/${expectedNodes.length}个已注册`);
      
    } catch (error) {
      this.results.particleSystem.errors.push(`粒子系统节点测试失败: ${error.message}`);
      console.log(`   ❌ 粒子系统节点测试失败: ${error.message}`);
    }
  }

  /**
   * 测试地形编辑节点
   */
  async testTerrainEditingNodes() {
    console.log('🏔️ 测试地形编辑节点集成...');
    
    try {
      const expectedNodes = [
        'TerrainSculpting',
        'TerrainPainting',
        'TerrainTexture',
        'TerrainVegetation',
        'TerrainWater',
        'TerrainOptimization',
        'TerrainExport',
        'TerrainImport',
        'TerrainHeightmap',
        'TerrainErosion',
        'TerrainGeneration',
        'TerrainAnalysis'
      ];

      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      const registryContent = fs.readFileSync(registryPath, 'utf8');

      let registeredCount = 0;
      expectedNodes.forEach(nodeType => {
        if (registryContent.includes(`'${nodeType}'`)) {
          registeredCount++;
        } else {
          this.results.terrainEditing.errors.push(`地形编辑节点未注册: ${nodeType}`);
        }
      });

      this.results.terrainEditing.nodeCount = registeredCount;
      this.results.terrainEditing.success = registeredCount === expectedNodes.length;

      console.log(`   ✅ 地形编辑节点: ${registeredCount}/${expectedNodes.length}个已注册`);
      
    } catch (error) {
      this.results.terrainEditing.errors.push(`地形编辑节点测试失败: ${error.message}`);
      console.log(`   ❌ 地形编辑节点测试失败: ${error.message}`);
    }
  }

  /**
   * 测试动作捕捉节点
   */
  async testMotionCaptureNodes() {
    console.log('🎥 测试动作捕捉节点集成...');
    
    try {
      const expectedNodes = [
        'CameraInput',
        'MotionCaptureInit',
        'SkeletonTracking',
        'FaceTracking',
        'HandTracking',
        'BodyTracking'
      ];

      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      const registryContent = fs.readFileSync(registryPath, 'utf8');

      let registeredCount = 0;
      expectedNodes.forEach(nodeType => {
        if (registryContent.includes(`'${nodeType}'`)) {
          registeredCount++;
        } else {
          this.results.motionCapture.errors.push(`动作捕捉节点未注册: ${nodeType}`);
        }
      });

      this.results.motionCapture.nodeCount = registeredCount;
      this.results.motionCapture.success = registeredCount === expectedNodes.length;

      console.log(`   ✅ 动作捕捉节点: ${registeredCount}/${expectedNodes.length}个已注册`);
      
    } catch (error) {
      this.results.motionCapture.errors.push(`动作捕捉节点测试失败: ${error.message}`);
      console.log(`   ❌ 动作捕捉节点测试失败: ${error.message}`);
    }
  }

  /**
   * 测试计算机视觉节点
   */
  async testComputerVisionNodes() {
    console.log('👁️ 测试计算机视觉节点集成...');
    
    try {
      const expectedNodes = [
        'ObjectDetection',
        'ImageClassification',
        'FeatureExtraction',
        'ImageSegmentation',
        'ObjectTracking',
        'FaceRecognition',
        'OpticalCharacterRecognition',
        'ImageGeneration',
        'StyleTransfer',
        'ImageEnhancement',
        'AugmentedReality',
        'ImageFilter',
        'EdgeDetection',
        'StereoVision',
        'MotionTracking'
      ];

      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      const registryContent = fs.readFileSync(registryPath, 'utf8');

      let registeredCount = 0;
      expectedNodes.forEach(nodeType => {
        if (registryContent.includes(`'${nodeType}'`)) {
          registeredCount++;
        } else {
          this.results.computerVision.errors.push(`计算机视觉节点未注册: ${nodeType}`);
        }
      });

      this.results.computerVision.nodeCount = registeredCount;
      this.results.computerVision.success = registeredCount === expectedNodes.length;

      console.log(`   ✅ 计算机视觉节点: ${registeredCount}/${expectedNodes.length}个已注册`);
      
    } catch (error) {
      this.results.computerVision.errors.push(`计算机视觉节点测试失败: ${error.message}`);
      console.log(`   ❌ 计算机视觉节点测试失败: ${error.message}`);
    }
  }

  /**
   * 测试主NodeRegistry集成
   */
  async testMainRegistryIntegration() {
    console.log('📋 测试主NodeRegistry集成...');
    
    try {
      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      const registryContent = fs.readFileSync(registryPath, 'utf8');

      // 检查注册函数是否存在
      const requiredFunctions = [
        'registerParticleSystemNodes',
        'registerTerrainEditingNodes',
        'registerMotionCaptureNodes',
        'registerComputerVisionNodes'
      ];

      requiredFunctions.forEach(func => {
        if (!registryContent.includes(func)) {
          this.results.overall.errors.push(`主NodeRegistry中缺少函数: ${func}`);
        }
      });

      // 检查批次节点注册调用
      if (!registryContent.includes('registerParticleSystemNodes()')) {
        this.results.overall.errors.push('主NodeRegistry中未调用registerParticleSystemNodes()');
      }
      if (!registryContent.includes('registerTerrainEditingNodes()')) {
        this.results.overall.errors.push('主NodeRegistry中未调用registerTerrainEditingNodes()');
      }
      if (!registryContent.includes('registerMotionCaptureNodes()')) {
        this.results.overall.errors.push('主NodeRegistry中未调用registerMotionCaptureNodes()');
      }
      if (!registryContent.includes('registerComputerVisionNodes()')) {
        this.results.overall.errors.push('主NodeRegistry中未调用registerComputerVisionNodes()');
      }

      console.log(`   ✅ 主NodeRegistry集成检查完成`);
      
    } catch (error) {
      this.results.overall.errors.push(`主NodeRegistry集成测试失败: ${error.message}`);
      console.log(`   ❌ 主NodeRegistry集成测试失败: ${error.message}`);
    }
  }

  /**
   * 生成综合测试报告
   */
  generateComprehensiveReport() {
    console.log('\n📊 批次0.2其他系统集成综合测试报告');
    console.log('='.repeat(60));
    
    // 各系统测试结果
    console.log('\n🎆 粒子系统节点 (目标: 8个):');
    console.log(`   状态: ${this.results.particleSystem.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   已注册: ${this.results.particleSystem.nodeCount}/8个`);
    if (this.results.particleSystem.errors.length > 0) {
      console.log('   错误:');
      this.results.particleSystem.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n🏔️ 地形编辑节点 (目标: 12个):');
    console.log(`   状态: ${this.results.terrainEditing.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   已注册: ${this.results.terrainEditing.nodeCount}/12个`);
    if (this.results.terrainEditing.errors.length > 0) {
      console.log('   错误:');
      this.results.terrainEditing.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n🎥 动作捕捉节点 (目标: 6个):');
    console.log(`   状态: ${this.results.motionCapture.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   已注册: ${this.results.motionCapture.nodeCount}/6个`);
    if (this.results.motionCapture.errors.length > 0) {
      console.log('   错误:');
      this.results.motionCapture.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n👁️ 计算机视觉节点 (目标: 15个):');
    console.log(`   状态: ${this.results.computerVision.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   已注册: ${this.results.computerVision.nodeCount}/15个`);
    if (this.results.computerVision.errors.length > 0) {
      console.log('   错误:');
      this.results.computerVision.errors.forEach(error => console.log(`     - ${error}`));
    }

    // 总体统计
    const totalRegistered = this.results.particleSystem.nodeCount + 
                           this.results.terrainEditing.nodeCount + 
                           this.results.motionCapture.nodeCount + 
                           this.results.computerVision.nodeCount;
    
    const totalTarget = 8 + 12 + 6 + 15; // 41个节点
    const overallSuccess = this.results.particleSystem.success && 
                          this.results.terrainEditing.success && 
                          this.results.motionCapture.success && 
                          this.results.computerVision.success;

    console.log('\n🎯 总体结果:');
    console.log(`   状态: ${overallSuccess ? '✅ 全部成功' : '❌ 存在问题'}`);
    console.log(`   已完成节点: ${totalRegistered}/${totalTarget}个`);
    console.log(`   完成率: ${Math.round((totalRegistered / totalTarget) * 100)}%`);
    
    if (this.results.overall.errors.length > 0) {
      console.log('\n   系统级错误:');
      this.results.overall.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n📝 下一步工作:');
    console.log('   - 完成服务器集成扩展节点 (52个)');
    console.log('   - 完善编辑器UI界面集成');
    console.log('   - 进行全面功能测试');
    
    console.log('\n' + '='.repeat(60));
  }
}

// 运行测试
if (require.main === module) {
  const test = new Batch02OtherSystemsIntegrationTest();
  test.runAllTests().catch(console.error);
}

module.exports = Batch02OtherSystemsIntegrationTest;
