/**
 * 地形高级编辑节点
 * 提供地形高度图、侵蚀、生成、分析等高级功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2 } from 'three';

/**
 * 高度图格式枚举
 */
export enum HeightmapFormat {
  PNG = 'png',
  TIFF = 'tiff',
  EXR = 'exr',
  RAW = 'raw',
  CUSTOM = 'custom'
}

/**
 * 侵蚀类型枚举
 */
export enum ErosionType {
  THERMAL = 'thermal',
  HYDRAULIC = 'hydraulic',
  WIND = 'wind',
  CHEMICAL = 'chemical',
  COMBINED = 'combined'
}

/**
 * 地形高度图节点
 */
export class TerrainHeightmapNode extends VisualScriptNode {
  public static readonly TYPE = 'TerrainHeightmap';
  public static readonly NAME = '地形高度图';
  public static readonly DESCRIPTION = '地形高度图编辑器，提供基于高度图的地形生成和编辑';

  private heightmaps: Map<string, any> = new Map();

  constructor(nodeType: string = TerrainHeightmapNode.TYPE, name: string = TerrainHeightmapNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('loadHeightmap', 'trigger', '加载高度图');
    this.addInput('saveHeightmap', 'trigger', '保存高度图');
    this.addInput('generateTerrain', 'trigger', '生成地形');
    this.addInput('editHeightmap', 'trigger', '编辑高度图');
    this.addInput('heightmapFile', 'string', '高度图文件');
    this.addInput('format', 'string', '文件格式');
    this.addInput('resolution', 'number', '分辨率');
    this.addInput('heightScale', 'number', '高度缩放');
    this.addInput('terrainSize', 'vector3', '地形尺寸');
    this.addInput('position', 'vector2', '编辑位置');
    this.addInput('brushSize', 'number', '画刷大小');
    this.addInput('heightValue', 'number', '高度值');

    // 输出端口
    this.addOutput('heightmapId', 'string', '高度图ID');
    this.addOutput('terrainId', 'string', '地形ID');
    this.addOutput('heightmapData', 'object', '高度图数据');
    this.addOutput('terrainData', 'object', '地形数据');
    this.addOutput('onHeightmapLoaded', 'trigger', '高度图加载完成');
    this.addOutput('onTerrainGenerated', 'trigger', '地形生成完成');
    this.addOutput('onHeightmapEdited', 'trigger', '高度图编辑完成');
  }

  public execute(inputs: any): any {
    try {
      if (inputs?.loadHeightmap) {
        return this.loadHeightmap(inputs);
      } else if (inputs?.saveHeightmap) {
        return this.saveHeightmap(inputs);
      } else if (inputs?.generateTerrain) {
        return this.generateTerrain(inputs);
      } else if (inputs?.editHeightmap) {
        return this.editHeightmap(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('TerrainHeightmapNode', '高度图操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private loadHeightmap(inputs: any): any {
    const heightmapFile = inputs?.heightmapFile as string;
    const format = inputs?.format as HeightmapFormat || HeightmapFormat.PNG;
    const resolution = inputs?.resolution as number || 512;

    if (!heightmapFile) {
      Debug.warn('TerrainHeightmapNode', '加载高度图需要有效的文件路径');
      return this.getDefaultOutputs();
    }

    const heightmapId = `heightmap_${Date.now()}`;
    const heightmapData = {
      id: heightmapId,
      file: heightmapFile,
      format,
      resolution,
      width: resolution,
      height: resolution,
      data: new Float32Array(resolution * resolution),
      loaded: true,
      createdAt: Date.now()
    };

    this.heightmaps.set(heightmapId, heightmapData);

    Debug.log('TerrainHeightmapNode', `高度图加载成功: ${heightmapFile} (${heightmapId})`);

    return {
      heightmapId,
      terrainId: '',
      heightmapData,
      terrainData: null,
      onHeightmapLoaded: true,
      onTerrainGenerated: false,
      onHeightmapEdited: false
    };
  }

  private saveHeightmap(inputs: any): any {
    const heightmapId = inputs?.heightmapId as string;
    const heightmapFile = inputs?.heightmapFile as string;
    const format = inputs?.format as HeightmapFormat || HeightmapFormat.PNG;

    const heightmap = this.heightmaps.get(heightmapId);
    if (!heightmap) {
      Debug.warn('TerrainHeightmapNode', `高度图不存在: ${heightmapId}`);
      return this.getDefaultOutputs();
    }

    // 模拟保存操作
    Debug.log('TerrainHeightmapNode', `高度图保存成功: ${heightmapFile} (${format})`);

    return {
      heightmapId,
      terrainId: '',
      heightmapData: heightmap,
      terrainData: null,
      onHeightmapLoaded: false,
      onTerrainGenerated: false,
      onHeightmapEdited: false
    };
  }

  private generateTerrain(inputs: any): any {
    const heightmapId = inputs?.heightmapId as string;
    const heightScale = inputs?.heightScale as number || 100;
    const terrainSize = inputs?.terrainSize as Vector3 || new Vector3(1000, 100, 1000);

    const heightmap = this.heightmaps.get(heightmapId);
    if (!heightmap) {
      Debug.warn('TerrainHeightmapNode', `高度图不存在: ${heightmapId}`);
      return this.getDefaultOutputs();
    }

    const terrainId = `terrain_${Date.now()}`;
    const terrainData = {
      id: terrainId,
      heightmapId,
      size: terrainSize,
      heightScale,
      vertices: this.generateVertices(heightmap, terrainSize, heightScale),
      indices: this.generateIndices(heightmap.resolution),
      generated: true,
      createdAt: Date.now()
    };

    Debug.log('TerrainHeightmapNode', `地形生成成功: ${terrainId} from ${heightmapId}`);

    return {
      heightmapId,
      terrainId,
      heightmapData: heightmap,
      terrainData,
      onHeightmapLoaded: false,
      onTerrainGenerated: true,
      onHeightmapEdited: false
    };
  }

  private editHeightmap(inputs: any): any {
    const heightmapId = inputs?.heightmapId as string;
    const position = inputs?.position as Vector2;
    const brushSize = inputs?.brushSize as number || 10;
    const heightValue = inputs?.heightValue as number || 0.5;

    const heightmap = this.heightmaps.get(heightmapId);
    if (!heightmap || !position) {
      Debug.warn('TerrainHeightmapNode', '编辑高度图需要有效的高度图ID和位置');
      return this.getDefaultOutputs();
    }

    // 模拟编辑操作
    this.applyHeightEdit(heightmap, position, brushSize, heightValue);

    Debug.log('TerrainHeightmapNode', `高度图编辑完成: ${heightmapId} at (${position.x}, ${position.y})`);

    return {
      heightmapId,
      terrainId: '',
      heightmapData: heightmap,
      terrainData: null,
      onHeightmapLoaded: false,
      onTerrainGenerated: false,
      onHeightmapEdited: true
    };
  }

  private generateVertices(heightmap: any, terrainSize: Vector3, heightScale: number): Float32Array {
    const resolution = heightmap.resolution;
    const vertices = new Float32Array(resolution * resolution * 3);
    
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = (z * resolution + x) * 3;
        const heightIndex = z * resolution + x;
        
        vertices[index] = (x / (resolution - 1)) * terrainSize.x - terrainSize.x / 2;
        vertices[index + 1] = heightmap.data[heightIndex] * heightScale;
        vertices[index + 2] = (z / (resolution - 1)) * terrainSize.z - terrainSize.z / 2;
      }
    }
    
    return vertices;
  }

  private generateIndices(resolution: number): Uint32Array {
    const indices = new Uint32Array((resolution - 1) * (resolution - 1) * 6);
    let index = 0;
    
    for (let z = 0; z < resolution - 1; z++) {
      for (let x = 0; x < resolution - 1; x++) {
        const topLeft = z * resolution + x;
        const topRight = topLeft + 1;
        const bottomLeft = (z + 1) * resolution + x;
        const bottomRight = bottomLeft + 1;
        
        // 第一个三角形
        indices[index++] = topLeft;
        indices[index++] = bottomLeft;
        indices[index++] = topRight;
        
        // 第二个三角形
        indices[index++] = topRight;
        indices[index++] = bottomLeft;
        indices[index++] = bottomRight;
      }
    }
    
    return indices;
  }

  private applyHeightEdit(heightmap: any, position: Vector2, brushSize: number, heightValue: number): void {
    const resolution = heightmap.resolution;
    const centerX = Math.floor(position.x * resolution);
    const centerY = Math.floor(position.y * resolution);
    const radius = Math.floor(brushSize / 2);
    
    for (let y = Math.max(0, centerY - radius); y <= Math.min(resolution - 1, centerY + radius); y++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(resolution - 1, centerX + radius); x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        if (distance <= radius) {
          const falloff = 1 - (distance / radius);
          const index = y * resolution + x;
          heightmap.data[index] = heightValue * falloff + heightmap.data[index] * (1 - falloff);
        }
      }
    }
  }

  private getDefaultOutputs(): any {
    return {
      heightmapId: '',
      terrainId: '',
      heightmapData: null,
      terrainData: null,
      onHeightmapLoaded: false,
      onTerrainGenerated: false,
      onHeightmapEdited: false
    };
  }

  /**
   * 获取所有高度图
   */
  public getAllHeightmaps(): Map<string, any> {
    return new Map(this.heightmaps);
  }

  /**
   * 清除所有高度图
   */
  public clearAllHeightmaps(): void {
    this.heightmaps.clear();
    Debug.log('TerrainHeightmapNode', '所有高度图已清除');
  }
}

/**
 * 地形侵蚀节点
 */
export class TerrainErosionNode extends VisualScriptNode {
  public static readonly TYPE = 'TerrainErosion';
  public static readonly NAME = '地形侵蚀';
  public static readonly DESCRIPTION = '地形侵蚀效果工具，模拟自然侵蚀过程';

  private erosionSessions: Map<string, any> = new Map();

  constructor(nodeType: string = TerrainErosionNode.TYPE, name: string = TerrainErosionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('startErosion', 'trigger', '开始侵蚀');
    this.addInput('stopErosion', 'trigger', '停止侵蚀');
    this.addInput('applyErosion', 'trigger', '应用侵蚀');
    this.addInput('terrainId', 'string', '地形ID');
    this.addInput('erosionType', 'string', '侵蚀类型');
    this.addInput('intensity', 'number', '侵蚀强度');
    this.addInput('iterations', 'number', '迭代次数');
    this.addInput('rainAmount', 'number', '降雨量');
    this.addInput('evaporation', 'number', '蒸发率');
    this.addInput('sedimentCapacity', 'number', '沉积物容量');
    this.addInput('thermalRate', 'number', '热侵蚀率');

    // 输出端口
    this.addOutput('sessionId', 'string', '侵蚀会话ID');
    this.addOutput('erosionData', 'object', '侵蚀数据');
    this.addOutput('progress', 'number', '侵蚀进度');
    this.addOutput('onErosionStarted', 'trigger', '侵蚀开始');
    this.addOutput('onErosionCompleted', 'trigger', '侵蚀完成');
    this.addOutput('onErosionStopped', 'trigger', '侵蚀停止');
  }

  public execute(inputs: any): any {
    try {
      if (inputs?.startErosion) {
        return this.startErosion(inputs);
      } else if (inputs?.stopErosion) {
        return this.stopErosion(inputs);
      } else if (inputs?.applyErosion) {
        return this.applyErosion(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('TerrainErosionNode', '地形侵蚀操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private startErosion(inputs: any): any {
    const terrainId = inputs?.terrainId as string;
    const erosionType = inputs?.erosionType as ErosionType || ErosionType.HYDRAULIC;
    const intensity = inputs?.intensity as number || 0.5;
    const iterations = inputs?.iterations as number || 100;

    if (!terrainId) {
      Debug.warn('TerrainErosionNode', '开始侵蚀需要有效的地形ID');
      return this.getDefaultOutputs();
    }

    const sessionId = `erosion_${Date.now()}`;
    const erosionData = {
      id: sessionId,
      terrainId,
      type: erosionType,
      intensity,
      iterations,
      currentIteration: 0,
      rainAmount: inputs?.rainAmount || 0.1,
      evaporation: inputs?.evaporation || 0.05,
      sedimentCapacity: inputs?.sedimentCapacity || 0.3,
      thermalRate: inputs?.thermalRate || 0.1,
      running: true,
      startedAt: Date.now()
    };

    this.erosionSessions.set(sessionId, erosionData);

    Debug.log('TerrainErosionNode', `地形侵蚀开始: ${erosionType} on ${terrainId} (${sessionId})`);

    return {
      sessionId,
      erosionData,
      progress: 0,
      onErosionStarted: true,
      onErosionCompleted: false,
      onErosionStopped: false
    };
  }

  private stopErosion(inputs: any): any {
    const sessionId = inputs?.sessionId as string;
    const session = this.erosionSessions.get(sessionId);

    if (!session) {
      Debug.warn('TerrainErosionNode', `侵蚀会话不存在: ${sessionId}`);
      return this.getDefaultOutputs();
    }

    session.running = false;
    session.stoppedAt = Date.now();

    Debug.log('TerrainErosionNode', `地形侵蚀停止: ${sessionId}`);

    return {
      sessionId,
      erosionData: session,
      progress: session.currentIteration / session.iterations,
      onErosionStarted: false,
      onErosionCompleted: false,
      onErosionStopped: true
    };
  }

  private applyErosion(inputs: any): any {
    const sessionId = inputs?.sessionId as string;
    const session = this.erosionSessions.get(sessionId);

    if (!session || !session.running) {
      Debug.warn('TerrainErosionNode', `无效的侵蚀会话: ${sessionId}`);
      return this.getDefaultOutputs();
    }

    // 模拟侵蚀迭代
    session.currentIteration++;
    const progress = session.currentIteration / session.iterations;
    const completed = session.currentIteration >= session.iterations;

    if (completed) {
      session.running = false;
      session.completedAt = Date.now();
    }

    Debug.log('TerrainErosionNode', `侵蚀迭代: ${session.currentIteration}/${session.iterations} (${Math.round(progress * 100)}%)`);

    return {
      sessionId,
      erosionData: session,
      progress,
      onErosionStarted: false,
      onErosionCompleted: completed,
      onErosionStopped: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      sessionId: '',
      erosionData: null,
      progress: 0,
      onErosionStarted: false,
      onErosionCompleted: false,
      onErosionStopped: false
    };
  }

  /**
   * 获取所有侵蚀会话
   */
  public getAllErosionSessions(): Map<string, any> {
    return new Map(this.erosionSessions);
  }

  /**
   * 清除所有侵蚀会话
   */
  public clearAllSessions(): void {
    this.erosionSessions.clear();
    Debug.log('TerrainErosionNode', '所有侵蚀会话已清除');
  }
}
