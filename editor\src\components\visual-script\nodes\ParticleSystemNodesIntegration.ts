/**
 * 粒子系统节点编辑器集成
 * 为粒子系统节点提供专用的编辑器界面和可视化组件
 */

import { NodeEditor } from '../NodeEditor';
import { NodeInfo } from '../../../types/NodeTypes';

export class ParticleSystemNodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Set<string> = new Set();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
  }

  /**
   * 集成所有粒子系统节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成粒子系统节点到编辑器...');

    // 集成粒子系统编辑器节点
    this.integrateParticleSystemEditorNode();

    // 集成粒子发射器编辑器节点
    this.integrateParticleEmitterEditorNode();

    // 集成粒子预览节点
    this.integrateParticlePreviewNode();

    // 集成粒子库节点
    this.integrateParticleLibraryNode();

    // 集成粒子导入导出节点
    this.integrateParticleImportExportNodes();

    // 集成粒子力场编辑器节点
    this.integrateParticleForceEditorNode();

    // 集成粒子碰撞编辑器节点
    this.integrateParticleCollisionEditorNode();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('粒子系统节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成粒子系统编辑器节点
   */
  private integrateParticleSystemEditorNode(): void {
    this.registerNode({
      type: 'ParticleSystemEditor',
      name: '粒子系统编辑器',
      description: '粒子系统编辑器，提供完整的粒子系统创建和编辑功能',
      category: 'Particle/System',
      icon: 'grain',
      color: '#E91E63',
      tags: ['particle', 'system', 'editor'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showProperties: true,
        showTimeline: true,
        customPanels: ['particleSystemProperties', 'emitterList', 'previewPanel']
      }
    });

    console.log('粒子系统编辑器节点集成完成');
  }

  /**
   * 集成粒子发射器编辑器节点
   */
  private integrateParticleEmitterEditorNode(): void {
    this.registerNode({
      type: 'ParticleEmitterEditor',
      name: '粒子发射器编辑器',
      description: '粒子发射器编辑器，提供发射器的详细配置和编辑功能',
      category: 'Particle/Emitter',
      icon: 'grain',
      color: '#E91E63',
      tags: ['particle', 'emitter', 'editor'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showProperties: true,
        customPanels: ['emitterProperties', 'shapeEditor', 'emissionSettings']
      }
    });

    console.log('粒子发射器编辑器节点集成完成');
  }

  /**
   * 集成粒子预览节点
   */
  private integrateParticlePreviewNode(): void {
    this.registerNode({
      type: 'ParticlePreview',
      name: '粒子预览',
      description: '粒子预览器，提供实时粒子效果预览和播放控制',
      category: 'Particle/Preview',
      icon: 'play_circle',
      color: '#E91E63',
      tags: ['particle', 'preview', 'playback'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showPlaybackControls: true,
        customPanels: ['previewViewport', 'playbackControls', 'statisticsPanel']
      }
    });

    console.log('粒子预览节点集成完成');
  }

  /**
   * 集成粒子库节点
   */
  private integrateParticleLibraryNode(): void {
    this.registerNode({
      type: 'ParticleLibrary',
      name: '粒子库',
      description: '粒子效果库管理器，提供预设效果的管理和应用功能',
      category: 'Particle/Library',
      icon: 'library_books',
      color: '#E91E63',
      tags: ['particle', 'library', 'presets'],
      nodeClass: null,
      editorConfig: {
        showLibrary: true,
        showPresets: true,
        customPanels: ['presetLibrary', 'categoryFilter', 'presetPreview']
      }
    });

    console.log('粒子库节点集成完成');
  }

  /**
   * 集成粒子导入导出节点
   */
  private integrateParticleImportExportNodes(): void {
    // 粒子导出节点
    this.registerNode({
      type: 'ParticleExport',
      name: '粒子导出',
      description: '粒子效果导出器，支持多种格式的粒子效果导出',
      category: 'Particle/IO',
      icon: 'file_download',
      color: '#E91E63',
      tags: ['particle', 'export', 'file'],
      nodeClass: null,
      editorConfig: {
        showFileDialog: true,
        showFormatOptions: true,
        customPanels: ['exportSettings', 'formatSelector', 'progressPanel']
      }
    });

    // 粒子导入节点
    this.registerNode({
      type: 'ParticleImport',
      name: '粒子导入',
      description: '粒子效果导入器，支持多种格式的粒子效果导入',
      category: 'Particle/IO',
      icon: 'file_upload',
      color: '#E91E63',
      tags: ['particle', 'import', 'file'],
      nodeClass: null,
      editorConfig: {
        showFileDialog: true,
        showFormatOptions: true,
        customPanels: ['importSettings', 'fileSelector', 'previewPanel']
      }
    });

    console.log('粒子导入导出节点集成完成');
  }

  /**
   * 集成粒子力场编辑器节点
   */
  private integrateParticleForceEditorNode(): void {
    this.registerNode({
      type: 'ParticleForceEditor',
      name: '粒子力场编辑器',
      description: '粒子力场编辑器，用于配置影响粒子的各种力场',
      category: 'Particle/Physics',
      icon: 'force',
      color: '#E91E63',
      tags: ['particle', 'force', 'physics'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showProperties: true,
        customPanels: ['forceFieldList', 'forceProperties', 'visualizationPanel']
      }
    });

    console.log('粒子力场编辑器节点集成完成');
  }

  /**
   * 集成粒子碰撞编辑器节点
   */
  private integrateParticleCollisionEditorNode(): void {
    this.registerNode({
      type: 'ParticleCollisionEditor',
      name: '粒子碰撞编辑器',
      description: '粒子碰撞编辑器，用于配置粒子与环境的碰撞检测',
      category: 'Particle/Physics',
      icon: 'collision',
      color: '#E91E63',
      tags: ['particle', 'collision', 'physics'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showProperties: true,
        customPanels: ['colliderList', 'collisionProperties', 'debugVisualization']
      }
    });

    console.log('粒子碰撞编辑器节点集成完成');
  }

  /**
   * 注册节点到编辑器
   */
  private registerNode(nodeInfo: NodeInfo & { editorConfig?: any }): void {
    // 注册到节点编辑器
    this.nodeEditor.registerNode(nodeInfo);

    // 添加到已注册节点集合
    this.registeredNodes.add(nodeInfo.type);

    // 添加到分类映射
    const category = nodeInfo.category;
    if (!this.categoryNodes.has(category)) {
      this.categoryNodes.set(category, []);
    }
    this.categoryNodes.get(category)!.push(nodeInfo.type);

    // 如果有编辑器配置，设置自定义面板
    if (nodeInfo.editorConfig) {
      this.setupCustomPanels(nodeInfo.type, nodeInfo.editorConfig);
    }
  }

  /**
   * 设置自定义面板
   */
  private setupCustomPanels(nodeType: string, editorConfig: any): void {
    if (editorConfig.customPanels) {
      editorConfig.customPanels.forEach((panelType: string) => {
        this.nodeEditor.addCustomPanel(nodeType, panelType, this.createPanelConfig(panelType));
      });
    }
  }

  /**
   * 创建面板配置
   */
  private createPanelConfig(panelType: string): any {
    const panelConfigs: { [key: string]: any } = {
      particleSystemProperties: {
        title: '粒子系统属性',
        icon: 'settings',
        resizable: true,
        collapsible: true
      },
      emitterList: {
        title: '发射器列表',
        icon: 'list',
        resizable: true,
        collapsible: true
      },
      previewPanel: {
        title: '预览面板',
        icon: 'visibility',
        resizable: true,
        collapsible: false
      },
      emitterProperties: {
        title: '发射器属性',
        icon: 'tune',
        resizable: true,
        collapsible: true
      },
      shapeEditor: {
        title: '形状编辑器',
        icon: 'crop_free',
        resizable: true,
        collapsible: true
      },
      emissionSettings: {
        title: '发射设置',
        icon: 'settings_input_component',
        resizable: true,
        collapsible: true
      },
      previewViewport: {
        title: '预览视口',
        icon: 'videocam',
        resizable: true,
        collapsible: false
      },
      playbackControls: {
        title: '播放控制',
        icon: 'play_arrow',
        resizable: false,
        collapsible: false
      },
      statisticsPanel: {
        title: '统计信息',
        icon: 'analytics',
        resizable: true,
        collapsible: true
      },
      presetLibrary: {
        title: '预设库',
        icon: 'folder',
        resizable: true,
        collapsible: true
      },
      categoryFilter: {
        title: '分类筛选',
        icon: 'filter_list',
        resizable: false,
        collapsible: true
      },
      presetPreview: {
        title: '预设预览',
        icon: 'preview',
        resizable: true,
        collapsible: true
      },
      exportSettings: {
        title: '导出设置',
        icon: 'settings',
        resizable: true,
        collapsible: true
      },
      formatSelector: {
        title: '格式选择',
        icon: 'description',
        resizable: false,
        collapsible: true
      },
      progressPanel: {
        title: '进度面板',
        icon: 'progress_activity',
        resizable: false,
        collapsible: false
      },
      importSettings: {
        title: '导入设置',
        icon: 'settings',
        resizable: true,
        collapsible: true
      },
      fileSelector: {
        title: '文件选择',
        icon: 'folder_open',
        resizable: true,
        collapsible: true
      },
      forceFieldList: {
        title: '力场列表',
        icon: 'list',
        resizable: true,
        collapsible: true
      },
      forceProperties: {
        title: '力场属性',
        icon: 'tune',
        resizable: true,
        collapsible: true
      },
      visualizationPanel: {
        title: '可视化面板',
        icon: 'visibility',
        resizable: true,
        collapsible: true
      },
      colliderList: {
        title: '碰撞器列表',
        icon: 'list',
        resizable: true,
        collapsible: true
      },
      collisionProperties: {
        title: '碰撞属性',
        icon: 'tune',
        resizable: true,
        collapsible: true
      },
      debugVisualization: {
        title: '调试可视化',
        icon: 'bug_report',
        resizable: true,
        collapsible: true
      }
    };

    return panelConfigs[panelType] || {
      title: panelType,
      icon: 'extension',
      resizable: true,
      collapsible: true
    };
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 粒子系统节点面板
    const particleSystemPalette = {
      category: '粒子系统',
      nodes: Array.from(this.registeredNodes)
    };

    this.nodeEditor.addNodePalette(particleSystemPalette);
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    this.categoryNodes.forEach((nodes, category) => {
      this.nodeEditor.addNodeCategory({
        name: category,
        displayName: this.getCategoryDisplayName(category),
        icon: this.getCategoryIcon(category),
        color: '#E91E63',
        nodes: nodes
      });
    });
  }

  /**
   * 获取分类显示名称
   */
  private getCategoryDisplayName(category: string): string {
    const displayNames: { [key: string]: string } = {
      'Particle/System': '粒子系统',
      'Particle/Emitter': '粒子发射器',
      'Particle/Preview': '粒子预览',
      'Particle/Library': '粒子库',
      'Particle/IO': '粒子导入导出',
      'Particle/Physics': '粒子物理'
    };

    return displayNames[category] || category;
  }

  /**
   * 获取分类图标
   */
  private getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'Particle/System': 'grain',
      'Particle/Emitter': 'scatter_plot',
      'Particle/Preview': 'play_circle',
      'Particle/Library': 'library_books',
      'Particle/IO': 'import_export',
      'Particle/Physics': 'physics'
    };

    return icons[category] || 'extension';
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): any {
    return {
      totalNodes: this.registeredNodes.size,
      nodesByCategory: Object.fromEntries(this.categoryNodes),
      categories: Array.from(this.categoryNodes.keys())
    };
  }
}
