/**
 * IK系统配置面板组件
 * 为IKSystemNode提供专用的配置界面
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Button,
  Input,
  Select,
  Slider,
  Switch,
  Tabs,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  message,
  Tree,
  Collapse,
  Progress,
  Alert
} from 'antd';
import {
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  LinkOutlined,
  AimOutlined,
  BranchesOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * IK链配置接口
 */
interface IKChain {
  id: string;
  name: string;
  rootBone: string;
  endEffector: string;
  target: string;
  chainLength: number;
  iterations: number;
  tolerance: number;
  weight: number;
  enabled: boolean;
  constraints: IKConstraint[];
}

/**
 * IK约束接口
 */
interface IKConstraint {
  id: string;
  type: 'position' | 'rotation' | 'pole' | 'hinge' | 'twist';
  bone: string;
  weight: number;
  enabled: boolean;
  parameters: { [key: string]: any };
}

/**
 * IK目标接口
 */
interface IKTarget {
  id: string;
  name: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  weight: number;
  enabled: boolean;
}

/**
 * IK系统面板属性
 */
interface IKSystemPanelProps {
  nodeId?: string;
  onChainChange?: (chains: IKChain[]) => void;
  onTargetChange?: (targets: IKTarget[]) => void;
  readonly?: boolean;
}

/**
 * IK系统面板组件
 */
export const IKSystemPanel: React.FC<IKSystemPanelProps> = ({
  nodeId,
  onChainChange,
  onTargetChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  
  // 状态管理
  const [ikChains, setIkChains] = useState<IKChain[]>([]);
  const [ikTargets, setIkTargets] = useState<IKTarget[]>([]);
  const [selectedChain, setSelectedChain] = useState<string>('');
  const [selectedTarget, setSelectedTarget] = useState<string>('');
  const [isEnabled, setIsEnabled] = useState(true);
  const [globalWeight, setGlobalWeight] = useState(1.0);
  
  // UI状态
  const [showChainModal, setShowChainModal] = useState(false);
  const [showTargetModal, setShowTargetModal] = useState(false);
  const [showConstraintModal, setShowConstraintModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [visualizationEnabled, setVisualizationEnabled] = useState(true);

  // 表单实例
  const [chainForm] = Form.useForm();
  const [targetForm] = Form.useForm();
  const [constraintForm] = Form.useForm();

  /**
   * 初始化默认数据
   */
  useEffect(() => {
    const defaultChains: IKChain[] = [
      {
        id: 'left_arm',
        name: '左臂IK链',
        rootBone: 'LeftShoulder',
        endEffector: 'LeftHand',
        target: 'left_hand_target',
        chainLength: 3,
        iterations: 10,
        tolerance: 0.001,
        weight: 1.0,
        enabled: true,
        constraints: [
          {
            id: 'elbow_pole',
            type: 'pole',
            bone: 'LeftElbow',
            weight: 1.0,
            enabled: true,
            parameters: {
              poleTarget: 'left_elbow_pole',
              angle: 0
            }
          }
        ]
      },
      {
        id: 'right_arm',
        name: '右臂IK链',
        rootBone: 'RightShoulder',
        endEffector: 'RightHand',
        target: 'right_hand_target',
        chainLength: 3,
        iterations: 10,
        tolerance: 0.001,
        weight: 1.0,
        enabled: true,
        constraints: []
      }
    ];

    const defaultTargets: IKTarget[] = [
      {
        id: 'left_hand_target',
        name: '左手目标',
        position: { x: -0.5, y: 1.0, z: 0.3 },
        rotation: { x: 0, y: 0, z: 0 },
        weight: 1.0,
        enabled: true
      },
      {
        id: 'right_hand_target',
        name: '右手目标',
        position: { x: 0.5, y: 1.0, z: 0.3 },
        rotation: { x: 0, y: 0, z: 0 },
        weight: 1.0,
        enabled: true
      }
    ];

    setIkChains(defaultChains);
    setIkTargets(defaultTargets);
  }, []);

  /**
   * IK链管理
   */
  const handleAddChain = useCallback(() => {
    setEditingItem(null);
    chainForm.resetFields();
    setShowChainModal(true);
  }, [chainForm]);

  const handleEditChain = useCallback((chain: IKChain) => {
    setEditingItem(chain);
    chainForm.setFieldsValue(chain);
    setShowChainModal(true);
  }, [chainForm]);

  const handleDeleteChain = useCallback((chainId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个IK链吗？',
      onOk: () => {
        const newChains = ikChains.filter(c => c.id !== chainId);
        setIkChains(newChains);
        onChainChange?.(newChains);
        message.success('IK链已删除');
      }
    });
  }, [ikChains, onChainChange]);

  /**
   * IK目标管理
   */
  const handleAddTarget = useCallback(() => {
    setEditingItem(null);
    targetForm.resetFields();
    setShowTargetModal(true);
  }, [targetForm]);

  const handleEditTarget = useCallback((target: IKTarget) => {
    setEditingItem(target);
    targetForm.setFieldsValue(target);
    setShowTargetModal(true);
  }, [targetForm]);

  /**
   * 约束管理
   */
  const handleAddConstraint = useCallback((chainId: string) => {
    setEditingItem({ chainId });
    constraintForm.resetFields();
    setShowConstraintModal(true);
  }, [constraintForm]);

  /**
   * 渲染IK链列表
   */
  const renderIKChains = () => (
    <Card 
      title="IK链配置" 
      size="small"
      extra={
        !readonly && (
          <Button 
            type="primary" 
            size="small" 
            icon={<PlusOutlined />}
            onClick={handleAddChain}
          >
            添加IK链
          </Button>
        )
      }
    >
      <Collapse size="small">
        {ikChains.map(chain => (
          <Panel
            key={chain.id}
            header={
              <Row align="middle" justify="space-between" style={{ width: '100%' }}>
                <Col>
                  <Space>
                    <Tag color={chain.enabled ? 'green' : 'red'}>
                      {chain.name}
                    </Tag>
                    <Text type="secondary">
                      {chain.rootBone} → {chain.endEffector}
                    </Text>
                  </Space>
                </Col>
                <Col>
                  {!readonly && (
                    <Space onClick={(e) => e.stopPropagation()}>
                      <Button 
                        size="small" 
                        icon={<EditOutlined />}
                        onClick={() => handleEditChain(chain)}
                      />
                      <Button 
                        size="small" 
                        danger 
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeleteChain(chain.id)}
                      />
                    </Space>
                  )}
                </Col>
              </Row>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>目标: </Text>
                  <Tag>{chain.target}</Tag>
                </Col>
                <Col span={12}>
                  <Text strong>链长度: </Text>
                  <Text>{chain.chainLength}</Text>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Text strong>权重</Text>
                  <Slider
                    min={0}
                    max={1}
                    step={0.01}
                    value={chain.weight}
                    onChange={(value) => {
                      const newChains = ikChains.map(c => 
                        c.id === chain.id ? { ...c, weight: value } : c
                      );
                      setIkChains(newChains);
                      onChainChange?.(newChains);
                    }}
                    disabled={readonly}
                  />
                </Col>
                <Col span={8}>
                  <Text strong>迭代次数</Text>
                  <InputNumber
                    min={1}
                    max={50}
                    value={chain.iterations}
                    onChange={(value) => {
                      const newChains = ikChains.map(c => 
                        c.id === chain.id ? { ...c, iterations: value || 10 } : c
                      );
                      setIkChains(newChains);
                      onChainChange?.(newChains);
                    }}
                    disabled={readonly}
                    size="small"
                  />
                </Col>
                <Col span={8}>
                  <Text strong>容差</Text>
                  <InputNumber
                    min={0.0001}
                    max={0.1}
                    step={0.0001}
                    value={chain.tolerance}
                    onChange={(value) => {
                      const newChains = ikChains.map(c => 
                        c.id === chain.id ? { ...c, tolerance: value || 0.001 } : c
                      );
                      setIkChains(newChains);
                      onChainChange?.(newChains);
                    }}
                    disabled={readonly}
                    size="small"
                  />
                </Col>
              </Row>

              <div>
                <Row align="middle" justify="space-between">
                  <Col>
                    <Text strong>约束 ({chain.constraints.length})</Text>
                  </Col>
                  <Col>
                    {!readonly && (
                      <Button 
                        size="small" 
                        icon={<PlusOutlined />}
                        onClick={() => handleAddConstraint(chain.id)}
                      >
                        添加约束
                      </Button>
                    )}
                  </Col>
                </Row>
                
                {chain.constraints.map(constraint => (
                  <Card key={constraint.id} size="small" style={{ marginTop: 8 }}>
                    <Row align="middle" justify="space-between">
                      <Col>
                        <Space>
                          <Tag color="blue">{constraint.type}</Tag>
                          <Text>{constraint.bone}</Text>
                          <Text type="secondary">权重: {constraint.weight}</Text>
                        </Space>
                      </Col>
                      <Col>
                        <Switch 
                          size="small" 
                          checked={constraint.enabled}
                          disabled={readonly}
                        />
                      </Col>
                    </Row>
                  </Card>
                ))}
              </div>
            </Space>
          </Panel>
        ))}
      </Collapse>
    </Card>
  );

  /**
   * 渲染IK目标列表
   */
  const renderIKTargets = () => (
    <Card 
      title="IK目标" 
      size="small"
      extra={
        !readonly && (
          <Button 
            type="primary" 
            size="small" 
            icon={<PlusOutlined />}
            onClick={handleAddTarget}
          >
            添加目标
          </Button>
        )
      }
    >
      {ikTargets.map(target => (
        <Card key={target.id} size="small" style={{ marginBottom: 8 }}>
          <Row align="middle" justify="space-between">
            <Col>
              <Space>
                <Tag color={target.enabled ? 'green' : 'red'}>
                  {target.name}
                </Tag>
                <Text type="secondary">权重: {target.weight}</Text>
              </Space>
            </Col>
            <Col>
              {!readonly && (
                <Space>
                  <Button 
                    size="small" 
                    icon={<EditOutlined />}
                    onClick={() => handleEditTarget(target)}
                  />
                  <Button size="small" danger icon={<DeleteOutlined />} />
                </Space>
              )}
            </Col>
          </Row>
          
          <Row gutter={8} style={{ marginTop: 8 }}>
            <Col span={12}>
              <Text strong>位置</Text>
              <div>
                <Text type="secondary">
                  X: {target.position.x.toFixed(2)}, 
                  Y: {target.position.y.toFixed(2)}, 
                  Z: {target.position.z.toFixed(2)}
                </Text>
              </div>
            </Col>
            <Col span={12}>
              <Text strong>旋转</Text>
              <div>
                <Text type="secondary">
                  X: {target.rotation.x.toFixed(2)}, 
                  Y: {target.rotation.y.toFixed(2)}, 
                  Z: {target.rotation.z.toFixed(2)}
                </Text>
              </div>
            </Col>
          </Row>
        </Card>
      ))}
    </Card>
  );

  /**
   * 渲染系统控制
   */
  const renderSystemControls = () => (
    <Card title="系统控制" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Text strong>启用IK系统</Text>
          </Col>
          <Col>
            <Switch 
              checked={isEnabled}
              onChange={setIsEnabled}
              disabled={readonly}
            />
          </Col>
        </Row>
        
        <Row align="middle">
          <Col span={8}>
            <Text strong>全局权重</Text>
          </Col>
          <Col span={16}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={globalWeight}
              onChange={setGlobalWeight}
              disabled={readonly}
            />
          </Col>
        </Row>
        
        <Row align="middle" justify="space-between">
          <Col>
            <Text strong>可视化调试</Text>
          </Col>
          <Col>
            <Switch 
              checked={visualizationEnabled}
              onChange={setVisualizationEnabled}
              disabled={readonly}
              checkedChildren={<EyeOutlined />}
              unCheckedChildren={<EyeInvisibleOutlined />}
            />
          </Col>
        </Row>

        <Alert
          message="IK系统状态"
          description={`当前有 ${ikChains.filter(c => c.enabled).length} 个活动IK链，${ikTargets.filter(t => t.enabled).length} 个活动目标`}
          type="info"
          showIcon
          size="small"
        />
      </Space>
    </Card>
  );

  return (
    <div className="ik-system-panel">
      <Card
        title={
          <Space>
            <LinkOutlined />
            <Title level={4} style={{ margin: 0 }}>
              IK系统配置
            </Title>
          </Space>
        }
        size="small"
      >
        <Tabs defaultActiveKey="chains" size="small">
          <TabPane tab="IK链" key="chains">
            {renderIKChains()}
          </TabPane>
          <TabPane tab="目标" key="targets">
            {renderIKTargets()}
          </TabPane>
          <TabPane tab="控制" key="controls">
            {renderSystemControls()}
          </TabPane>
        </Tabs>
      </Card>

      {/* IK链编辑模态框 */}
      <Modal
        title={editingItem ? "编辑IK链" : "添加IK链"}
        visible={showChainModal}
        onCancel={() => setShowChainModal(false)}
        onOk={() => chainForm.submit()}
        width={600}
      >
        <Form
          form={chainForm}
          layout="vertical"
          onFinish={(values) => {
            const newChain: IKChain = {
              id: editingItem?.id || `chain_${Date.now()}`,
              constraints: editingItem?.constraints || [],
              ...values
            };
            
            let newChains;
            if (editingItem) {
              newChains = ikChains.map(c => c.id === editingItem.id ? newChain : c);
            } else {
              newChains = [...ikChains, newChain];
            }
            
            setIkChains(newChains);
            onChainChange?.(newChains);
            setShowChainModal(false);
            message.success(editingItem ? 'IK链已更新' : 'IK链已添加');
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="链名称" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="target" label="目标" rules={[{ required: true }]}>
                <Select>
                  {ikTargets.map(target => (
                    <Option key={target.id} value={target.id}>
                      {target.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="rootBone" label="根骨骼" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="endEffector" label="末端执行器" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="chainLength" label="链长度" initialValue={3}>
                <InputNumber min={1} max={10} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="iterations" label="迭代次数" initialValue={10}>
                <InputNumber min={1} max={50} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="tolerance" label="容差" initialValue={0.001}>
                <InputNumber min={0.0001} max={0.1} step={0.0001} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="weight" label="权重" initialValue={1.0}>
                <InputNumber min={0} max={1} step={0.01} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="enabled" label="启用" valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default IKSystemPanel;
