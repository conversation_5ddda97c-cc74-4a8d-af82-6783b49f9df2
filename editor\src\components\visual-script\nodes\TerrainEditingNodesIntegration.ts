/**
 * 地形编辑节点编辑器集成
 * 为地形编辑节点提供专用的编辑器界面和可视化组件
 */

import { NodeEditor } from '../NodeEditor';
import { NodeInfo } from '../../../types/NodeTypes';

export class TerrainEditingNodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Set<string> = new Set();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
  }

  /**
   * 集成所有地形编辑节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成地形编辑节点到编辑器...');

    // 集成地形雕刻节点
    this.integrateTerrainSculptingNode();

    // 集成地形绘制节点
    this.integrateTerrainPaintingNode();

    // 集成地形纹理节点
    this.integrateTerrainTextureNode();

    // 集成地形植被节点
    this.integrateTerrainVegetationNode();

    // 集成地形水体节点
    this.integrateTerrainWaterNode();

    // 集成地形优化节点
    this.integrateTerrainOptimizationNode();

    // 集成地形导入导出节点
    this.integrateTerrainImportExportNodes();

    // 集成地形高度图节点
    this.integrateTerrainHeightmapNode();

    // 集成地形侵蚀节点
    this.integrateTerrainErosionNode();

    // 集成其他地形节点
    this.integrateOtherTerrainNodes();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('地形编辑节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成地形雕刻节点
   */
  private integrateTerrainSculptingNode(): void {
    this.registerNode({
      type: 'TerrainSculpting',
      name: '地形雕刻',
      description: '地形雕刻工具，提供抬升、降低、平整、平滑等雕刻功能',
      category: 'Terrain/Sculpting',
      icon: 'terrain',
      color: '#8BC34A',
      tags: ['terrain', 'sculpting', 'editing'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showBrushSettings: true,
        showUndoRedo: true,
        customPanels: ['sculptingTools', 'brushSettings', 'terrainPreview', 'operationHistory']
      }
    });

    console.log('地形雕刻节点集成完成');
  }

  /**
   * 集成地形绘制节点
   */
  private integrateTerrainPaintingNode(): void {
    this.registerNode({
      type: 'TerrainPainting',
      name: '地形绘制',
      description: '地形绘制工具，提供纹理绘制和材质混合功能',
      category: 'Terrain/Painting',
      icon: 'brush',
      color: '#8BC34A',
      tags: ['terrain', 'painting', 'texture'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showTextureLibrary: true,
        showBrushSettings: true,
        customPanels: ['paintingTools', 'textureLibrary', 'layerManager', 'blendingSettings']
      }
    });

    console.log('地形绘制节点集成完成');
  }

  /**
   * 集成地形纹理节点
   */
  private integrateTerrainTextureNode(): void {
    this.registerNode({
      type: 'TerrainTexture',
      name: '地形纹理',
      description: '地形纹理管理器，提供多层纹理混合和材质编辑功能',
      category: 'Terrain/Texture',
      icon: 'texture',
      color: '#8BC34A',
      tags: ['terrain', 'texture', 'material'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showTextureLibrary: true,
        showLayerManager: true,
        customPanels: ['textureManager', 'layerBlending', 'materialEditor', 'uvMapping']
      }
    });

    console.log('地形纹理节点集成完成');
  }

  /**
   * 集成地形植被节点
   */
  private integrateTerrainVegetationNode(): void {
    this.registerNode({
      type: 'TerrainVegetation',
      name: '地形植被',
      description: '地形植被系统，提供草地、树木、灌木等植被的放置和管理',
      category: 'Terrain/Vegetation',
      icon: 'nature',
      color: '#8BC34A',
      tags: ['terrain', 'vegetation', 'nature'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showVegetationLibrary: true,
        showDensitySettings: true,
        customPanels: ['vegetationLibrary', 'placementTools', 'densityMap', 'windSettings']
      }
    });

    console.log('地形植被节点集成完成');
  }

  /**
   * 集成地形水体节点
   */
  private integrateTerrainWaterNode(): void {
    this.registerNode({
      type: 'TerrainWater',
      name: '地形水体',
      description: '地形水体系统，提供河流、湖泊、海洋等水体的创建和编辑',
      category: 'Terrain/Water',
      icon: 'water',
      color: '#8BC34A',
      tags: ['terrain', 'water', 'fluid'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showFlowSettings: true,
        showWaterProperties: true,
        customPanels: ['waterTypes', 'flowEditor', 'waterProperties', 'reflectionSettings']
      }
    });

    console.log('地形水体节点集成完成');
  }

  /**
   * 集成地形优化节点
   */
  private integrateTerrainOptimizationNode(): void {
    this.registerNode({
      type: 'TerrainOptimization',
      name: '地形优化',
      description: '地形性能优化工具，提供LOD、剔除、压缩等优化功能',
      category: 'Terrain/Optimization',
      icon: 'speed',
      color: '#8BC34A',
      tags: ['terrain', 'optimization', 'performance'],
      nodeClass: null,
      editorConfig: {
        showPerformanceStats: true,
        showLODSettings: true,
        showOptimizationOptions: true,
        customPanels: ['lodSettings', 'cullingOptions', 'compressionSettings', 'performanceMonitor']
      }
    });

    console.log('地形优化节点集成完成');
  }

  /**
   * 集成地形导入导出节点
   */
  private integrateTerrainImportExportNodes(): void {
    // 地形导出节点
    this.registerNode({
      type: 'TerrainExport',
      name: '地形导出',
      description: '地形导出工具，支持多种格式的地形数据导出',
      category: 'Terrain/IO',
      icon: 'file_download',
      color: '#8BC34A',
      tags: ['terrain', 'export', 'file'],
      nodeClass: null,
      editorConfig: {
        showFileDialog: true,
        showFormatOptions: true,
        showExportSettings: true,
        customPanels: ['exportFormats', 'exportSettings', 'progressPanel', 'previewPanel']
      }
    });

    // 地形导入节点
    this.registerNode({
      type: 'TerrainImport',
      name: '地形导入',
      description: '地形导入工具，支持多种格式的地形数据导入',
      category: 'Terrain/IO',
      icon: 'file_upload',
      color: '#8BC34A',
      tags: ['terrain', 'import', 'file'],
      nodeClass: null,
      editorConfig: {
        showFileDialog: true,
        showFormatOptions: true,
        showImportSettings: true,
        customPanels: ['importFormats', 'fileSelector', 'importSettings', 'previewPanel']
      }
    });

    console.log('地形导入导出节点集成完成');
  }

  /**
   * 集成地形高度图节点
   */
  private integrateTerrainHeightmapNode(): void {
    this.registerNode({
      type: 'TerrainHeightmap',
      name: '地形高度图',
      description: '地形高度图编辑器，提供基于高度图的地形生成和编辑',
      category: 'Terrain/Heightmap',
      icon: 'map',
      color: '#8BC34A',
      tags: ['terrain', 'heightmap', 'generation'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showHeightmapEditor: true,
        showGenerationSettings: true,
        customPanels: ['heightmapEditor', 'generationSettings', 'brushTools', 'heightmapPreview']
      }
    });

    console.log('地形高度图节点集成完成');
  }

  /**
   * 集成地形侵蚀节点
   */
  private integrateTerrainErosionNode(): void {
    this.registerNode({
      type: 'TerrainErosion',
      name: '地形侵蚀',
      description: '地形侵蚀效果工具，模拟自然侵蚀过程',
      category: 'Terrain/Erosion',
      icon: 'waves',
      color: '#8BC34A',
      tags: ['terrain', 'erosion', 'simulation'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showSimulationSettings: true,
        showProgressMonitor: true,
        customPanels: ['erosionTypes', 'simulationSettings', 'progressMonitor', 'resultPreview']
      }
    });

    console.log('地形侵蚀节点集成完成');
  }

  /**
   * 集成其他地形节点
   */
  private integrateOtherTerrainNodes(): void {
    // 地形生成节点
    this.registerNode({
      type: 'TerrainGeneration',
      name: '地形生成',
      description: '程序化地形生成器，基于噪声和算法生成地形',
      category: 'Terrain/Generation',
      icon: 'auto_awesome',
      color: '#8BC34A',
      tags: ['terrain', 'generation', 'procedural'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showNoiseSettings: true,
        showAlgorithmOptions: true,
        customPanels: ['noiseSettings', 'algorithmSelector', 'seedSettings', 'generationPreview']
      }
    });

    // 地形分析节点
    this.registerNode({
      type: 'TerrainAnalysis',
      name: '地形分析',
      description: '地形分析工具，提供坡度、曲率、可见性等分析功能',
      category: 'Terrain/Analysis',
      icon: 'analytics',
      color: '#8BC34A',
      tags: ['terrain', 'analysis', 'tools'],
      nodeClass: null,
      editorConfig: {
        showAnalysisResults: true,
        showVisualization: true,
        showStatistics: true,
        customPanels: ['analysisTools', 'visualizationOptions', 'statisticsPanel', 'resultsViewer']
      }
    });

    console.log('其他地形节点集成完成');
  }

  /**
   * 注册节点到编辑器
   */
  private registerNode(nodeInfo: NodeInfo & { editorConfig?: any }): void {
    // 注册到节点编辑器
    this.nodeEditor.registerNode(nodeInfo);

    // 添加到已注册节点集合
    this.registeredNodes.add(nodeInfo.type);

    // 添加到分类映射
    const category = nodeInfo.category;
    if (!this.categoryNodes.has(category)) {
      this.categoryNodes.set(category, []);
    }
    this.categoryNodes.get(category)!.push(nodeInfo.type);

    // 如果有编辑器配置，设置自定义面板
    if (nodeInfo.editorConfig) {
      this.setupCustomPanels(nodeInfo.type, nodeInfo.editorConfig);
    }
  }

  /**
   * 设置自定义面板
   */
  private setupCustomPanels(nodeType: string, editorConfig: any): void {
    if (editorConfig.customPanels) {
      editorConfig.customPanels.forEach((panelType: string) => {
        this.nodeEditor.addCustomPanel(nodeType, panelType, this.createPanelConfig(panelType));
      });
    }
  }

  /**
   * 创建面板配置
   */
  private createPanelConfig(panelType: string): any {
    const panelConfigs: { [key: string]: any } = {
      sculptingTools: { title: '雕刻工具', icon: 'build', resizable: true, collapsible: true },
      brushSettings: { title: '画刷设置', icon: 'brush', resizable: true, collapsible: true },
      terrainPreview: { title: '地形预览', icon: 'visibility', resizable: true, collapsible: false },
      operationHistory: { title: '操作历史', icon: 'history', resizable: true, collapsible: true },
      paintingTools: { title: '绘制工具', icon: 'palette', resizable: true, collapsible: true },
      textureLibrary: { title: '纹理库', icon: 'library_books', resizable: true, collapsible: true },
      layerManager: { title: '图层管理', icon: 'layers', resizable: true, collapsible: true },
      blendingSettings: { title: '混合设置', icon: 'tune', resizable: true, collapsible: true },
      textureManager: { title: '纹理管理', icon: 'texture', resizable: true, collapsible: true },
      layerBlending: { title: '图层混合', icon: 'layers', resizable: true, collapsible: true },
      materialEditor: { title: '材质编辑器', icon: 'edit', resizable: true, collapsible: true },
      uvMapping: { title: 'UV映射', icon: 'map', resizable: true, collapsible: true },
      vegetationLibrary: { title: '植被库', icon: 'nature', resizable: true, collapsible: true },
      placementTools: { title: '放置工具', icon: 'place', resizable: true, collapsible: true },
      densityMap: { title: '密度图', icon: 'scatter_plot', resizable: true, collapsible: true },
      windSettings: { title: '风力设置', icon: 'air', resizable: true, collapsible: true },
      waterTypes: { title: '水体类型', icon: 'water_drop', resizable: true, collapsible: true },
      flowEditor: { title: '流动编辑器', icon: 'waves', resizable: true, collapsible: true },
      waterProperties: { title: '水体属性', icon: 'settings', resizable: true, collapsible: true },
      reflectionSettings: { title: '反射设置', icon: 'flip', resizable: true, collapsible: true },
      lodSettings: { title: 'LOD设置', icon: 'tune', resizable: true, collapsible: true },
      cullingOptions: { title: '剔除选项', icon: 'visibility_off', resizable: true, collapsible: true },
      compressionSettings: { title: '压缩设置', icon: 'compress', resizable: true, collapsible: true },
      performanceMonitor: { title: '性能监控', icon: 'monitor', resizable: true, collapsible: true },
      exportFormats: { title: '导出格式', icon: 'description', resizable: false, collapsible: true },
      exportSettings: { title: '导出设置', icon: 'settings', resizable: true, collapsible: true },
      progressPanel: { title: '进度面板', icon: 'progress_activity', resizable: false, collapsible: false },
      importFormats: { title: '导入格式', icon: 'description', resizable: false, collapsible: true },
      fileSelector: { title: '文件选择', icon: 'folder_open', resizable: true, collapsible: true },
      importSettings: { title: '导入设置', icon: 'settings', resizable: true, collapsible: true },
      previewPanel: { title: '预览面板', icon: 'preview', resizable: true, collapsible: true },
      heightmapEditor: { title: '高度图编辑器', icon: 'edit', resizable: true, collapsible: false },
      generationSettings: { title: '生成设置', icon: 'settings', resizable: true, collapsible: true },
      brushTools: { title: '画刷工具', icon: 'brush', resizable: true, collapsible: true },
      heightmapPreview: { title: '高度图预览', icon: 'map', resizable: true, collapsible: true },
      erosionTypes: { title: '侵蚀类型', icon: 'category', resizable: false, collapsible: true },
      simulationSettings: { title: '模拟设置', icon: 'settings', resizable: true, collapsible: true },
      progressMonitor: { title: '进度监控', icon: 'monitor', resizable: true, collapsible: false },
      resultPreview: { title: '结果预览', icon: 'preview', resizable: true, collapsible: true },
      noiseSettings: { title: '噪声设置', icon: 'grain', resizable: true, collapsible: true },
      algorithmSelector: { title: '算法选择', icon: 'functions', resizable: false, collapsible: true },
      seedSettings: { title: '种子设置', icon: 'casino', resizable: true, collapsible: true },
      generationPreview: { title: '生成预览', icon: 'preview', resizable: true, collapsible: true },
      analysisTools: { title: '分析工具', icon: 'analytics', resizable: true, collapsible: true },
      visualizationOptions: { title: '可视化选项', icon: 'visibility', resizable: true, collapsible: true },
      statisticsPanel: { title: '统计面板', icon: 'bar_chart', resizable: true, collapsible: true },
      resultsViewer: { title: '结果查看器', icon: 'view_list', resizable: true, collapsible: true }
    };

    return panelConfigs[panelType] || {
      title: panelType,
      icon: 'extension',
      resizable: true,
      collapsible: true
    };
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 地形编辑节点面板
    const terrainEditingPalette = {
      category: '地形编辑',
      nodes: Array.from(this.registeredNodes)
    };

    this.nodeEditor.addNodePalette(terrainEditingPalette);
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    this.categoryNodes.forEach((nodes, category) => {
      this.nodeEditor.addNodeCategory({
        name: category,
        displayName: this.getCategoryDisplayName(category),
        icon: this.getCategoryIcon(category),
        color: '#8BC34A',
        nodes: nodes
      });
    });
  }

  /**
   * 获取分类显示名称
   */
  private getCategoryDisplayName(category: string): string {
    const displayNames: { [key: string]: string } = {
      'Terrain/Sculpting': '地形雕刻',
      'Terrain/Painting': '地形绘制',
      'Terrain/Texture': '地形纹理',
      'Terrain/Vegetation': '地形植被',
      'Terrain/Water': '地形水体',
      'Terrain/Optimization': '地形优化',
      'Terrain/IO': '地形导入导出',
      'Terrain/Heightmap': '地形高度图',
      'Terrain/Erosion': '地形侵蚀',
      'Terrain/Generation': '地形生成',
      'Terrain/Analysis': '地形分析'
    };

    return displayNames[category] || category;
  }

  /**
   * 获取分类图标
   */
  private getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'Terrain/Sculpting': 'terrain',
      'Terrain/Painting': 'brush',
      'Terrain/Texture': 'texture',
      'Terrain/Vegetation': 'nature',
      'Terrain/Water': 'water',
      'Terrain/Optimization': 'speed',
      'Terrain/IO': 'import_export',
      'Terrain/Heightmap': 'map',
      'Terrain/Erosion': 'waves',
      'Terrain/Generation': 'auto_awesome',
      'Terrain/Analysis': 'analytics'
    };

    return icons[category] || 'terrain';
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): any {
    return {
      totalNodes: this.registeredNodes.size,
      nodesByCategory: Object.fromEntries(this.categoryNodes),
      categories: Array.from(this.categoryNodes.keys())
    };
  }
}
