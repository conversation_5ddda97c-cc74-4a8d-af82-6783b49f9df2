/**
 * 视觉脚本节点注册表
 * 批次1.7、批次2.1和批次3.2节点注册
 * 注册所有新开发的动画系统增强节点、用户服务节点和边缘计算节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';

// 导入批次1.7动画系统增强节点
import { 
  AnimationBlendTreeNode,
  AnimationStateMachineNode,
  IKSystemNode,
  AnimationRetargetingNode,
  AnimationCompressionNode,
  AnimationOptimizationNode
} from './animation/AnimationNodes';

import {
  AnimationBakingNode,
  AnimationExportNode,
  AnimationImportNode,
  AnimationValidationNode
} from './animation/AnimationToolNodes';

// 导入批次2.1用户服务节点
import {
  UserAuthenticationNode,
  UserRegistrationNode
} from './user/UserServiceNodes';

import {
  UserProfileNode
} from './user/UserServiceNodes';

import {
  UserPermissionNode,
  UserRoleNode
} from './user/UserServiceNodes2';

import {
  UserSessionNode,
  UserPreferencesNode
} from './user/UserServiceNodes3';

import {
  UserActivityNode,
  UserAnalyticsNode
} from './user/UserServiceNodes4';

import {
  UserNotificationNode,
  UserGroupNode,
  UserSyncNode
} from './user/UserServiceNodes5';

// 导入批次3.2边缘路由节点
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from './edge/EdgeRoutingNodes';

import {
  EdgeOptimizationNode,
  EdgeQoSNode
} from './edge/EdgeRoutingNodes2';

// 导入批次3.2云边协调节点
import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from './edge/CloudEdgeNodes';

import {
  DataSynchronizationNode,
  TaskDistributionNode
} from './edge/CloudEdgeNodes2';

import {
  ResourceOptimizationNode,
  LatencyOptimizationNode
} from './edge/CloudEdgeNodes3';

import {
  BandwidthOptimizationNode,
  CostOptimizationNode
} from './edge/CloudEdgeNodes4';

// 导入批次3.2 5G网络节点
import {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from './edge/FiveGNetworkNodes';

import {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from './edge/FiveGNetworkNodes2';

import {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from './edge/FiveGNetworkNodes3';

/**
 * 节点注册表类
 */
export class NodeRegistry {
  private static instance: NodeRegistry;
  private nodeTypes: Map<string, typeof VisualScriptNode> = new Map();
  private nodeCategories: Map<string, string[]> = new Map();

  private constructor() {
    this.registerNodes();
  }

  public static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  /**
   * 注册所有节点
   */
  private registerNodes(): void {
    // 注册批次1.7动画系统增强节点
    this.registerAnimationNodes();

    // 注册批次2.1用户服务节点
    this.registerUserServiceNodes();

    // 注册批次3.2边缘计算节点
    this.registerEdgeComputingNodes();

    // 注册批次0.2边缘计算节点（通过专门的注册表）
    this.initializeEdgeComputingNodesRegistry();
  }

  /**
   * 注册动画系统增强节点
   */
  private registerAnimationNodes(): void {
    const animationNodes = [
      AnimationBlendTreeNode,
      AnimationStateMachineNode,
      IKSystemNode,
      AnimationRetargetingNode,
      AnimationCompressionNode,
      AnimationOptimizationNode,
      AnimationBakingNode,
      AnimationExportNode,
      AnimationImportNode,
      AnimationValidationNode
    ];

    const animationCategory = 'Animation/Advanced';
    this.nodeCategories.set(animationCategory, []);

    for (const NodeClass of animationNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(animationCategory)?.push(NodeClass.TYPE);
    }

    console.log(`已注册 ${animationNodes.length} 个动画系统增强节点`);
  }

  /**
   * 注册用户服务节点
   */
  private registerUserServiceNodes(): void {
    const userServiceNodes = [
      UserAuthenticationNode,
      UserRegistrationNode,
      UserProfileNode,
      UserPermissionNode,
      UserRoleNode,
      UserSessionNode,
      UserPreferencesNode,
      UserActivityNode,
      UserAnalyticsNode,
      UserNotificationNode,
      UserGroupNode,
      UserSyncNode
    ];

    const userServiceCategory = 'User/Services';
    this.nodeCategories.set(userServiceCategory, []);

    for (const NodeClass of userServiceNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(userServiceCategory)?.push(NodeClass.TYPE);
    }

    console.log(`已注册 ${userServiceNodes.length} 个用户服务节点`);
  }

  /**
   * 注册边缘计算节点
   */
  private registerEdgeComputingNodes(): void {
    // 边缘路由节点
    const edgeRoutingNodes = [
      EdgeRoutingNode,
      EdgeLoadBalancingNode,
      EdgeCachingNode,
      EdgeCompressionNode,
      EdgeOptimizationNode,
      EdgeQoSNode
    ];

    const edgeRoutingCategory = 'Edge/Routing';
    this.nodeCategories.set(edgeRoutingCategory, []);

    for (const NodeClass of edgeRoutingNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(edgeRoutingCategory)?.push(NodeClass.TYPE);
    }

    // 云边协调节点
    const cloudEdgeNodes = [
      CloudEdgeOrchestrationNode,
      HybridComputingNode,
      DataSynchronizationNode,
      TaskDistributionNode,
      ResourceOptimizationNode,
      LatencyOptimizationNode,
      BandwidthOptimizationNode,
      CostOptimizationNode
    ];

    const cloudEdgeCategory = 'Edge/CloudEdge';
    this.nodeCategories.set(cloudEdgeCategory, []);

    for (const NodeClass of cloudEdgeNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(cloudEdgeCategory)?.push(NodeClass.TYPE);
    }

    // 5G网络节点
    const fiveGNodes = [
      FiveGConnectionNode,
      FiveGSlicingNode,
      FiveGQoSNode,
      FiveGLatencyNode,
      FiveGBandwidthNode,
      FiveGSecurityNode,
      FiveGMonitoringNode,
      FiveGOptimizationNode
    ];

    const fiveGCategory = 'Edge/5G';
    this.nodeCategories.set(fiveGCategory, []);

    for (const NodeClass of fiveGNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(fiveGCategory)?.push(NodeClass.TYPE);
    }

    console.log(`已注册 ${edgeRoutingNodes.length} 个边缘路由节点`);
    console.log(`已注册 ${cloudEdgeNodes.length} 个云边协调节点`);
    console.log(`已注册 ${fiveGNodes.length} 个5G网络节点`);
  }

  /**
   * 初始化边缘计算节点注册表
   */
  private initializeEdgeComputingNodesRegistry(): void {
    try {
      // 动态导入边缘计算节点注册表
      import('../registry/EdgeComputingNodesRegistry').then(module => {
        const { edgeComputingNodesRegistry } = module;
        console.log('边缘计算节点注册表已初始化');
        console.log(`新增边缘计算节点: ${edgeComputingNodesRegistry.getRegisteredNodeCount()}个`);
      }).catch(error => {
        console.warn('边缘计算节点注册表初始化失败:', error);
      });
    } catch (error) {
      console.warn('无法加载边缘计算节点注册表:', error);
    }
  }

  /**
   * 创建节点实例
   */
  public createNode(nodeType: string, id?: string): VisualScriptNode | null {
    const NodeClass = this.nodeTypes.get(nodeType);
    if (NodeClass) {
      return new NodeClass(nodeType, NodeClass.NAME, id);
    }
    return null;
  }

  /**
   * 获取所有注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodeTypes.keys());
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, string[]> {
    return new Map(this.nodeCategories);
  }

  /**
   * 获取指定分类的节点
   */
  public getNodesByCategory(category: string): string[] {
    return this.nodeCategories.get(category) || [];
  }

  /**
   * 检查节点类型是否已注册
   */
  public isNodeTypeRegistered(nodeType: string): boolean {
    return this.nodeTypes.has(nodeType);
  }

  /**
   * 注册单个节点
   */
  public registerNode(NodeClass: any, category: string): void {
    // 注册节点类型
    this.nodeTypes.set(NodeClass.TYPE, NodeClass);

    // 确保分类存在
    if (!this.nodeCategories.has(category)) {
      this.nodeCategories.set(category, []);
    }

    // 添加到分类
    const categoryNodes = this.nodeCategories.get(category);
    if (categoryNodes && !categoryNodes.includes(NodeClass.TYPE)) {
      categoryNodes.push(NodeClass.TYPE);
    }

    console.log(`已注册节点: ${NodeClass.TYPE} -> ${category}`);
  }

  /**
   * 获取节点信息
   */
  public getNodeInfo(nodeType: string): any {
    const NodeClass = this.nodeTypes.get(nodeType);
    if (NodeClass) {
      return {
        type: NodeClass.TYPE,
        name: NodeClass.NAME,
        description: NodeClass.DESCRIPTION || '无描述'
      };
    }
    return null;
  }

  /**
   * 获取所有节点信息
   */
  public getAllNodeInfo(): any[] {
    const nodeInfos: any[] = [];
    
    for (const [nodeType, NodeClass] of this.nodeTypes) {
      nodeInfos.push({
        type: NodeClass.TYPE,
        name: NodeClass.NAME,
        description: NodeClass.DESCRIPTION || '无描述'
      });
    }
    
    return nodeInfos;
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStatistics(): any {
    const totalNodes = this.nodeTypes.size;
    const categories = this.nodeCategories.size;
    const categoryStats: any = {};

    for (const [category, nodes] of this.nodeCategories) {
      categoryStats[category] = nodes.length;
    }

    return {
      totalNodes,
      categories,
      categoryStats,
      registeredAt: new Date().toISOString()
    };
  }
}

/**
 * 导出单例实例
 */
export const nodeRegistry = NodeRegistry.getInstance();

/**
 * 便捷函数：创建节点
 */
export function createNode(nodeType: string, id?: string): VisualScriptNode | null {
  return nodeRegistry.createNode(nodeType, id);
}

/**
 * 便捷函数：获取所有节点类型
 */
export function getRegisteredNodeTypes(): string[] {
  return nodeRegistry.getRegisteredNodeTypes();
}

/**
 * 便捷函数：获取节点分类
 */
export function getNodeCategories(): Map<string, string[]> {
  return nodeRegistry.getNodeCategories();
}

/**
 * 便捷函数：获取节点信息
 */
export function getNodeInfo(nodeType: string): any {
  return nodeRegistry.getNodeInfo(nodeType);
}

/**
 * 便捷函数：获取所有节点信息
 */
export function getAllNodeInfo(): any[] {
  return nodeRegistry.getAllNodeInfo();
}

/**
 * 便捷函数：获取统计信息
 */
export function getNodeStatistics(): any {
  return nodeRegistry.getNodeStatistics();
}

// 初始化节点注册表
console.log('DL引擎视觉脚本节点注册表已初始化');
console.log('批次1.7动画系统增强节点：10个');
console.log('批次2.1用户服务节点：12个');
console.log('批次3.2边缘计算节点：24个');
console.log('  - 边缘路由节点：6个');
console.log('  - 云边协调节点：8个');
console.log('  - 5G网络节点：8个');
console.log('总计新增节点：46个');

const stats = getNodeStatistics();
console.log('节点统计信息：', stats);
