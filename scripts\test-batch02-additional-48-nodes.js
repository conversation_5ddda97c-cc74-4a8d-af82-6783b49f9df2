/**
 * 批次0.2额外48个节点集成测试脚本
 * 验证粒子系统(8)、地形编辑(10)、动作捕捉(6)、其他模块(24)节点的注册和集成状态
 */

const fs = require('fs');
const path = require('path');

/**
 * 验证结果
 */
const results = {
  totalNodes: 48,
  expectedNodes: {
    particleSystem: 8,
    terrainEditing: 10,
    motionCapture: 6,
    otherModules: 24
  },
  foundNodes: {
    particleSystem: 0,
    terrainEditing: 0,
    motionCapture: 0,
    otherModules: 0
  },
  errors: [],
  warnings: []
};

/**
 * 预期的节点类型
 */
const expectedNodeTypes = {
  particleSystem: [
    'ParticleSystemEditorNode', 'ParticleEmitterEditorNode', 'ParticlePreviewNode', 'ParticleLibraryNode',
    'ParticleExportNode', 'ParticleImportNode', 'ParticleForceEditorNode', 'ParticleCollisionEditorNode'
  ],
  terrainEditing: [
    'TerrainSculptingNode', 'TerrainPaintingNode', 'TerrainTextureNode', 'TerrainVegetationNode', 'TerrainWaterNode',
    'TerrainOptimizationNode', 'TerrainExportNode', 'TerrainImportNode', 'TerrainHeightmapNode', 'TerrainErosionNode'
  ],
  motionCapture: [
    'CameraInputNode', 'MotionCaptureInitNode', 'SkeletonTrackingNode', 
    'FaceTrackingNode', 'HandTrackingNode', 'BodyTrackingNode'
  ],
  otherModules: [
    // 交互系统节点 (8个)
    'UserInteractionNode', 'TouchInteractionNode', 'MouseInteractionNode', 'KeyboardInteractionNode',
    'InteractionEventNode', 'InteractionStateNode', 'InteractionFeedbackNode', 'InteractionHistoryNode',
    // 头像系统节点 (8个)
    'AvatarCreationNode', 'AvatarCustomizationNode', 'FacialExpressionNode', 'AvatarAnimationNode',
    'AvatarPhysicsNode', 'AvatarClothingNode', 'AvatarSkinNode', 'AvatarEmotionNode',
    // 其他功能节点 (8个)
    'PerformanceProfilerNode', 'DebugVisualizationNode', 'MemoryManagementNode', 'ResourceMonitorNode',
    'ErrorHandlingNode', 'LoggingSystemNode', 'ConfigurationNode', 'UtilityToolsNode'
  ]
};

/**
 * 验证节点注册
 */
function validateNodeRegistration() {
  console.log('验证48个额外节点注册状态...');

  try {
    // 检查批次0.2注册表文件
    const registryPath = path.resolve('engine/src/visual-script/registry/Batch02NodesRegistry.ts');
    if (!fs.existsSync(registryPath)) {
      results.errors.push('Batch02NodesRegistry.ts 文件不存在');
      return;
    }

    const registryContent = fs.readFileSync(registryPath, 'utf8');

    // 验证注册方法
    const requiredMethods = [
      'registerParticleSystemNodes',
      'registerTerrainEditingNodes',
      'registerMotionCaptureNodes',
      'registerOtherModuleNodes'
    ];

    requiredMethods.forEach(method => {
      if (!registryContent.includes(method)) {
        results.errors.push(`缺少注册方法: ${method}`);
      }
    });

    // 验证节点类型
    Object.entries(expectedNodeTypes).forEach(([category, nodeTypes]) => {
      nodeTypes.forEach(nodeType => {
        if (registryContent.includes(nodeType)) {
          results.foundNodes[category]++;
        } else {
          results.warnings.push(`节点类型未在注册表中找到: ${nodeType}`);
        }
      });
    });

    console.log('✓ 节点注册验证完成');

  } catch (error) {
    results.errors.push(`节点注册验证失败: ${error.message}`);
  }
}

/**
 * 验证编辑器集成
 */
function validateEditorIntegration() {
  console.log('验证编辑器集成状态...');

  try {
    // 检查编辑器集成文件
    const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch02OtherSystemsIntegration.ts');
    if (!fs.existsSync(integrationPath)) {
      results.errors.push('Batch02OtherSystemsIntegration.ts 文件不存在');
      return;
    }

    const integrationContent = fs.readFileSync(integrationPath, 'utf8');

    // 验证集成方法
    const requiredIntegrationMethods = [
      'integrateParticleSystemNodes',
      'integrateTerrainEditingNodes',
      'integrateMotionCaptureNodes',
      'integrateOtherModuleNodes'
    ];

    requiredIntegrationMethods.forEach(method => {
      if (!integrationContent.includes(method)) {
        results.errors.push(`缺少编辑器集成方法: ${method}`);
      }
    });

    // 验证节点分类
    const expectedCategories = [
      'Particle/System',
      'Terrain/Editing',
      'MotionCapture/Tracking',
      'Interaction/System',
      'Avatar/System',
      'Utility/Tools'
    ];

    expectedCategories.forEach(category => {
      if (!integrationContent.includes(category)) {
        results.warnings.push(`节点分类未找到: ${category}`);
      }
    });

    // 验证节点显示名称
    const sampleNodeNames = [
      'ParticleSystemEditorNode', 'TerrainSculptingNode', 'SkeletonTrackingNode', 
      'UserInteractionNode', 'AvatarCreationNode', 'PerformanceProfilerNode'
    ];

    sampleNodeNames.forEach(nodeName => {
      if (!integrationContent.includes(nodeName)) {
        results.warnings.push(`节点名称未在集成文件中找到: ${nodeName}`);
      }
    });

    console.log('✓ 编辑器集成验证完成');

  } catch (error) {
    results.errors.push(`编辑器集成验证失败: ${error.message}`);
  }
}

/**
 * 验证节点分类和元数据
 */
function validateNodeMetadata() {
  console.log('验证节点元数据...');

  try {
    const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch02OtherSystemsIntegration.ts');
    const integrationContent = fs.readFileSync(integrationPath, 'utf8');

    // 验证中文显示名称
    const chineseNames = [
      '粒子系统编辑器', '地形雕刻', '骨骼追踪', '用户交互', '头像创建', '性能分析器'
    ];

    chineseNames.forEach(name => {
      if (!integrationContent.includes(name)) {
        results.warnings.push(`中文显示名称未找到: ${name}`);
      }
    });

    // 验证节点描述
    const descriptions = [
      '粒子系统编辑和管理', '地形雕刻和塑形工具', '骨骼追踪和识别', 
      '用户交互管理', '数字头像创建系统', '性能分析和监控'
    ];

    descriptions.forEach(desc => {
      if (!integrationContent.includes(desc)) {
        results.warnings.push(`节点描述未找到: ${desc}`);
      }
    });

    console.log('✓ 节点元数据验证完成');

  } catch (error) {
    results.errors.push(`节点元数据验证失败: ${error.message}`);
  }
}

/**
 * 打印结果
 */
function printResults() {
  console.log('\n=== 批次0.2额外48个节点验证结果 ===');
  console.log(`总节点数: ${results.totalNodes}`);
  
  console.log('\n节点发现统计:');
  Object.entries(results.foundNodes).forEach(([category, count]) => {
    const expected = results.expectedNodes[category];
    const percentage = ((count / expected) * 100).toFixed(1);
    console.log(`  ${category}: ${count}/${expected} (${percentage}%)`);
  });

  const totalFound = Object.values(results.foundNodes).reduce((sum, count) => sum + count, 0);
  const totalPercentage = ((totalFound / results.totalNodes) * 100).toFixed(1);
  console.log(`\n总体完成率: ${totalFound}/${results.totalNodes} (${totalPercentage}%)`);

  console.log('\n节点分类详情:');
  console.log(`  粒子系统节点: ${results.foundNodes.particleSystem}/8`);
  console.log(`  地形编辑节点: ${results.foundNodes.terrainEditing}/10`);
  console.log(`  动作捕捉节点: ${results.foundNodes.motionCapture}/6`);
  console.log(`  其他模块节点: ${results.foundNodes.otherModules}/24`);

  if (results.errors.length > 0) {
    console.log('\n❌ 错误:');
    results.errors.forEach(error => console.log(`  - ${error}`));
  }

  if (results.warnings.length > 0) {
    console.log('\n⚠️  警告:');
    results.warnings.forEach(warning => console.log(`  - ${warning}`));
  }

  if (results.errors.length === 0) {
    console.log('\n✅ 验证通过！批次0.2额外48个节点已成功注册和集成');
    console.log('包含：粒子系统(8) + 地形编辑(10) + 动作捕捉(6) + 其他模块(24) = 48个节点');
  } else {
    console.log('\n❌ 验证失败！请修复上述错误后重新验证');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('开始验证批次0.2额外48个节点...');

  validateNodeRegistration();
  validateEditorIntegration();
  validateNodeMetadata();

  printResults();

  // 返回验证结果
  process.exit(results.errors.length > 0 ? 1 : 0);
}

// 运行验证
main();
