/**
 * 音频混合器面板组件
 * 为AudioMixerNode提供专用的混音台界面
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Button,
  Input,
  Select,
  Slider,
  Switch,
  Tabs,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  message,
  Progress,
  Divider,
  Badge
} from 'antd';
import {
  SoundOutlined,
  AudioOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  MutedOutlined,
  CustomerServiceOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 音频通道接口
 */
interface AudioChannel {
  id: string;
  name: string;
  volume: number;
  pan: number;
  muted: boolean;
  solo: boolean;
  gain: number;
  highEQ: number;
  midEQ: number;
  lowEQ: number;
  effects: AudioEffect[];
  inputSource: string;
  level: number;
  peak: number;
}

/**
 * 音频效果接口
 */
interface AudioEffect {
  id: string;
  type: 'reverb' | 'delay' | 'chorus' | 'distortion' | 'compressor' | 'eq';
  name: string;
  enabled: boolean;
  parameters: { [key: string]: number };
}

/**
 * 音频混合器配置接口
 */
interface AudioMixerConfig {
  masterVolume: number;
  masterMuted: boolean;
  sampleRate: number;
  bufferSize: number;
  channels: number;
  enableMetering: boolean;
  enableEffects: boolean;
}

/**
 * 音频混合器面板属性
 */
interface AudioMixerPanelProps {
  nodeId?: string;
  onChannelChange?: (channels: AudioChannel[]) => void;
  onConfigChange?: (config: AudioMixerConfig) => void;
  readonly?: boolean;
}

/**
 * 音频混合器面板组件
 */
export const AudioMixerPanel: React.FC<AudioMixerPanelProps> = ({
  nodeId,
  onChannelChange,
  onConfigChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  
  // 状态管理
  const [channels, setChannels] = useState<AudioChannel[]>([]);
  const [config, setConfig] = useState<AudioMixerConfig>({
    masterVolume: 0.8,
    masterMuted: false,
    sampleRate: 44100,
    bufferSize: 4096,
    channels: 8,
    enableMetering: true,
    enableEffects: true
  });
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<string>('');
  
  // UI状态
  const [showChannelModal, setShowChannelModal] = useState(false);
  const [showEffectModal, setShowEffectModal] = useState(false);
  const [editingChannel, setEditingChannel] = useState<AudioChannel | null>(null);
  const [meteringEnabled, setMeteringEnabled] = useState(true);

  // 表单实例
  const [channelForm] = Form.useForm();
  const [effectForm] = Form.useForm();

  /**
   * 初始化默认通道
   */
  useEffect(() => {
    const defaultChannels: AudioChannel[] = Array.from({ length: 8 }, (_, index) => ({
      id: `channel_${index + 1}`,
      name: `通道 ${index + 1}`,
      volume: 0.7,
      pan: 0,
      muted: false,
      solo: false,
      gain: 0,
      highEQ: 0,
      midEQ: 0,
      lowEQ: 0,
      effects: [],
      inputSource: '',
      level: Math.random() * 0.8,
      peak: Math.random() * 0.9
    }));

    setChannels(defaultChannels);
  }, []);

  /**
   * 更新通道参数
   */
  const updateChannelParameter = useCallback((channelId: string, parameter: string, value: any) => {
    const newChannels = channels.map(channel => 
      channel.id === channelId ? { ...channel, [parameter]: value } : channel
    );
    setChannels(newChannels);
    onChannelChange?.(newChannels);
  }, [channels, onChannelChange]);

  /**
   * 更新配置
   */
  const updateConfig = useCallback((parameter: string, value: any) => {
    const newConfig = { ...config, [parameter]: value };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  }, [config, onConfigChange]);

  /**
   * 渲染单个通道条
   */
  const renderChannelStrip = (channel: AudioChannel) => (
    <Card 
      key={channel.id}
      size="small"
      style={{ 
        width: 120, 
        height: 500, 
        margin: '0 4px',
        backgroundColor: selectedChannel === channel.id ? '#f0f8ff' : undefined
      }}
      onClick={() => setSelectedChannel(channel.id)}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="small">
        {/* 通道名称 */}
        <Input
          value={channel.name}
          size="small"
          onChange={(e) => updateChannelParameter(channel.id, 'name', e.target.value)}
          disabled={readonly}
        />

        {/* 输入源选择 */}
        <Select
          value={channel.inputSource}
          size="small"
          style={{ width: '100%' }}
          placeholder="输入源"
          onChange={(value) => updateChannelParameter(channel.id, 'inputSource', value)}
          disabled={readonly}
        >
          <Option value="mic1">麦克风 1</Option>
          <Option value="mic2">麦克风 2</Option>
          <Option value="line1">线路输入 1</Option>
          <Option value="line2">线路输入 2</Option>
          <Option value="usb">USB音频</Option>
        </Select>

        {/* 增益控制 */}
        <div>
          <Text style={{ fontSize: 10 }}>增益</Text>
          <Slider
            vertical
            min={-20}
            max={20}
            step={0.1}
            value={channel.gain}
            onChange={(value) => updateChannelParameter(channel.id, 'gain', value)}
            style={{ height: 60 }}
            disabled={readonly}
          />
          <Text style={{ fontSize: 10 }}>{channel.gain.toFixed(1)}dB</Text>
        </div>

        {/* EQ控制 */}
        <div>
          <Text style={{ fontSize: 10 }}>高频</Text>
          <Slider
            min={-12}
            max={12}
            step={0.1}
            value={channel.highEQ}
            onChange={(value) => updateChannelParameter(channel.id, 'highEQ', value)}
            disabled={readonly}
            size="small"
          />
        </div>
        
        <div>
          <Text style={{ fontSize: 10 }}>中频</Text>
          <Slider
            min={-12}
            max={12}
            step={0.1}
            value={channel.midEQ}
            onChange={(value) => updateChannelParameter(channel.id, 'midEQ', value)}
            disabled={readonly}
            size="small"
          />
        </div>
        
        <div>
          <Text style={{ fontSize: 10 }}>低频</Text>
          <Slider
            min={-12}
            max={12}
            step={0.1}
            value={channel.lowEQ}
            onChange={(value) => updateChannelParameter(channel.id, 'lowEQ', value)}
            disabled={readonly}
            size="small"
          />
        </div>

        {/* 声像控制 */}
        <div>
          <Text style={{ fontSize: 10 }}>声像</Text>
          <Slider
            min={-1}
            max={1}
            step={0.01}
            value={channel.pan}
            onChange={(value) => updateChannelParameter(channel.id, 'pan', value)}
            disabled={readonly}
            size="small"
          />
        </div>

        {/* 效果器指示 */}
        {channel.effects.length > 0 && (
          <Badge count={channel.effects.length} size="small">
            <Button size="small" icon={<SettingOutlined />} />
          </Badge>
        )}

        {/* 静音和独奏按钮 */}
        <Row gutter={4}>
          <Col span={12}>
            <Button
              size="small"
              type={channel.muted ? "primary" : "default"}
              danger={channel.muted}
              onClick={() => updateChannelParameter(channel.id, 'muted', !channel.muted)}
              disabled={readonly}
            >
              M
            </Button>
          </Col>
          <Col span={12}>
            <Button
              size="small"
              type={channel.solo ? "primary" : "default"}
              onClick={() => updateChannelParameter(channel.id, 'solo', !channel.solo)}
              disabled={readonly}
            >
              S
            </Button>
          </Col>
        </Row>

        {/* 音量推子 */}
        <div style={{ height: 120, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Slider
            vertical
            min={0}
            max={1}
            step={0.01}
            value={channel.volume}
            onChange={(value) => updateChannelParameter(channel.id, 'volume', value)}
            style={{ height: 100 }}
            disabled={readonly}
          />
          <Text style={{ fontSize: 10 }}>{Math.round(channel.volume * 100)}</Text>
        </div>

        {/* 电平表 */}
        {meteringEnabled && (
          <div style={{ height: 40 }}>
            <Progress
              percent={channel.level * 100}
              size="small"
              strokeColor={channel.level > 0.8 ? '#ff4d4f' : channel.level > 0.6 ? '#faad14' : '#52c41a'}
              showInfo={false}
            />
            <Progress
              percent={channel.peak * 100}
              size="small"
              strokeColor="#ff4d4f"
              showInfo={false}
              style={{ marginTop: 2 }}
            />
          </div>
        )}
      </Space>
    </Card>
  );

  /**
   * 渲染主控制区
   */
  const renderMasterSection = () => (
    <Card title="主控制" size="small" style={{ width: 200 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 主音量 */}
        <div>
          <Text strong>主音量</Text>
          <Slider
            min={0}
            max={1}
            step={0.01}
            value={config.masterVolume}
            onChange={(value) => updateConfig('masterVolume', value)}
            disabled={readonly}
          />
          <Text>{Math.round(config.masterVolume * 100)}%</Text>
        </div>

        {/* 主静音 */}
        <Row align="middle" justify="space-between">
          <Col>
            <Text>主静音</Text>
          </Col>
          <Col>
            <Switch
              checked={config.masterMuted}
              onChange={(checked) => updateConfig('masterMuted', checked)}
              disabled={readonly}
            />
          </Col>
        </Row>

        {/* 播放控制 */}
        <Divider />
        <Space>
          <Button
            type={isPlaying ? "default" : "primary"}
            icon={<PlayCircleOutlined />}
            onClick={() => setIsPlaying(true)}
            disabled={readonly}
          >
            播放
          </Button>
          <Button
            icon={<PauseCircleOutlined />}
            onClick={() => setIsPlaying(false)}
            disabled={readonly}
          >
            暂停
          </Button>
          <Button
            icon={<StopOutlined />}
            onClick={() => setIsPlaying(false)}
            disabled={readonly}
          >
            停止
          </Button>
        </Space>

        {/* 系统状态 */}
        <Divider />
        <div>
          <Text strong>系统状态</Text>
          <div>
            <Text type="secondary">采样率: {config.sampleRate}Hz</Text>
          </div>
          <div>
            <Text type="secondary">缓冲区: {config.bufferSize}</Text>
          </div>
          <div>
            <Text type="secondary">通道数: {config.channels}</Text>
          </div>
        </div>

        {/* 电平表开关 */}
        <Row align="middle" justify="space-between">
          <Col>
            <Text>电平表</Text>
          </Col>
          <Col>
            <Switch
              checked={meteringEnabled}
              onChange={setMeteringEnabled}
              disabled={readonly}
            />
          </Col>
        </Row>
      </Space>
    </Card>
  );

  /**
   * 渲染效果器机架
   */
  const renderEffectsRack = () => {
    const selectedChannelData = channels.find(c => c.id === selectedChannel);
    
    if (!selectedChannelData) {
      return (
        <Card title="效果器机架" size="small">
          <Text type="secondary">请选择一个通道查看效果器</Text>
        </Card>
      );
    }

    return (
      <Card 
        title={`${selectedChannelData.name} - 效果器`}
        size="small"
        extra={
          !readonly && (
            <Button 
              type="primary" 
              size="small" 
              icon={<PlusOutlined />}
              onClick={() => setShowEffectModal(true)}
            >
              添加效果
            </Button>
          )
        }
      >
        {selectedChannelData.effects.length === 0 ? (
          <Text type="secondary">暂无效果器</Text>
        ) : (
          selectedChannelData.effects.map(effect => (
            <Card key={effect.id} size="small" style={{ marginBottom: 8 }}>
              <Row align="middle" justify="space-between">
                <Col>
                  <Space>
                    <Tag color="blue">{effect.type}</Tag>
                    <Text>{effect.name}</Text>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <Switch 
                      size="small" 
                      checked={effect.enabled}
                      disabled={readonly}
                    />
                    {!readonly && (
                      <Button size="small" danger icon={<DeleteOutlined />} />
                    )}
                  </Space>
                </Col>
              </Row>
              
              {/* 效果器参数 */}
              <div style={{ marginTop: 8 }}>
                {Object.entries(effect.parameters).map(([param, value]) => (
                  <Row key={param} align="middle" style={{ marginBottom: 4 }}>
                    <Col span={8}>
                      <Text style={{ fontSize: 12 }}>{param}</Text>
                    </Col>
                    <Col span={16}>
                      <Slider
                        min={0}
                        max={1}
                        step={0.01}
                        value={value}
                        size="small"
                        disabled={readonly}
                      />
                    </Col>
                  </Row>
                ))}
              </div>
            </Card>
          ))
        )}
      </Card>
    );
  };

  return (
    <div className="audio-mixer-panel">
      <Card
        title={
          <Space>
            <AudioOutlined />
            <Title level={4} style={{ margin: 0 }}>
              音频混合器
            </Title>
            <Tag color={isPlaying ? 'green' : 'default'}>
              {isPlaying ? '播放中' : '已停止'}
            </Tag>
          </Space>
        }
        size="small"
      >
        <Tabs defaultActiveKey="mixer" size="small">
          <TabPane tab="混音台" key="mixer">
            <div style={{ display: 'flex', overflowX: 'auto', padding: '8px 0' }}>
              {channels.map(renderChannelStrip)}
              {renderMasterSection()}
            </div>
          </TabPane>
          
          <TabPane tab="效果器" key="effects">
            {renderEffectsRack()}
          </TabPane>
          
          <TabPane tab="设置" key="settings">
            <Card title="音频设置" size="small">
              <Form layout="vertical">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="采样率">
                      <Select 
                        value={config.sampleRate}
                        onChange={(value) => updateConfig('sampleRate', value)}
                        disabled={readonly}
                      >
                        <Option value={44100}>44.1 kHz</Option>
                        <Option value={48000}>48 kHz</Option>
                        <Option value={96000}>96 kHz</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="缓冲区大小">
                      <Select 
                        value={config.bufferSize}
                        onChange={(value) => updateConfig('bufferSize', value)}
                        disabled={readonly}
                      >
                        <Option value={1024}>1024</Option>
                        <Option value={2048}>2048</Option>
                        <Option value={4096}>4096</Option>
                        <Option value={8192}>8192</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="启用电平表">
                      <Switch 
                        checked={config.enableMetering}
                        onChange={(checked) => updateConfig('enableMetering', checked)}
                        disabled={readonly}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="启用效果器">
                      <Switch 
                        checked={config.enableEffects}
                        onChange={(checked) => updateConfig('enableEffects', checked)}
                        disabled={readonly}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AudioMixerPanel;
