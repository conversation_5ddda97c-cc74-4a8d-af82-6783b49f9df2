/**
 * 物理性能监控面板组件
 * 为PhysicsPerformanceMonitorNode提供物理系统性能监控界面
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Switch,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Progress,
  Statistic,
  Alert,
  Table,
  Tabs,
  Select,
  Divider
} from 'antd';
import {
  DashboardOutlined,
  ThunderboltOutlined,
  Bar<PERSON>hartOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  physicsTime: number;
  collisionTime: number;
  integrationTime: number;
  broadPhaseTime: number;
  narrowPhaseTime: number;
  memoryUsage: number;
  objectCount: number;
  contactCount: number;
  constraintCount: number;
  islandCount: number;
}

/**
 * 性能历史记录接口
 */
interface PerformanceHistory {
  timestamp: number;
  metrics: PerformanceMetrics;
}

/**
 * 性能警告接口
 */
interface PerformanceWarning {
  id: string;
  type: 'fps' | 'memory' | 'objects' | 'time';
  severity: 'low' | 'medium' | 'high';
  message: string;
  timestamp: number;
  resolved: boolean;
}

/**
 * 物理性能监控面板属性
 */
interface PhysicsPerformanceMonitorPanelProps {
  nodeId?: string;
  onMetricsChange?: (metrics: PerformanceMetrics) => void;
  readonly?: boolean;
}

/**
 * 物理性能监控面板组件
 */
export const PhysicsPerformanceMonitorPanel: React.FC<PhysicsPerformanceMonitorPanelProps> = ({
  nodeId,
  onMetricsChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLCanvasElement>(null);
  const intervalRef = useRef<NodeJS.Timeout>();
  
  // 状态管理
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    physicsTime: 5.2,
    collisionTime: 2.1,
    integrationTime: 1.8,
    broadPhaseTime: 0.8,
    narrowPhaseTime: 1.3,
    memoryUsage: 45.6,
    objectCount: 150,
    contactCount: 23,
    constraintCount: 8,
    islandCount: 3
  });
  
  const [history, setHistory] = useState<PerformanceHistory[]>([]);
  const [warnings, setWarnings] = useState<PerformanceWarning[]>([]);
  const [selectedMetric, setSelectedMetric] = useState<string>('fps');
  const [autoOptimize, setAutoOptimize] = useState(false);

  /**
   * 生成模拟性能数据
   */
  const generateMetrics = useCallback((): PerformanceMetrics => {
    const baseMetrics = currentMetrics;
    
    return {
      fps: Math.max(30, baseMetrics.fps + (Math.random() - 0.5) * 10),
      frameTime: 1000 / Math.max(30, baseMetrics.fps + (Math.random() - 0.5) * 10),
      physicsTime: Math.max(1, baseMetrics.physicsTime + (Math.random() - 0.5) * 2),
      collisionTime: Math.max(0.5, baseMetrics.collisionTime + (Math.random() - 0.5) * 1),
      integrationTime: Math.max(0.5, baseMetrics.integrationTime + (Math.random() - 0.5) * 0.8),
      broadPhaseTime: Math.max(0.1, baseMetrics.broadPhaseTime + (Math.random() - 0.5) * 0.4),
      narrowPhaseTime: Math.max(0.2, baseMetrics.narrowPhaseTime + (Math.random() - 0.5) * 0.6),
      memoryUsage: Math.max(20, Math.min(90, baseMetrics.memoryUsage + (Math.random() - 0.5) * 5)),
      objectCount: Math.max(50, baseMetrics.objectCount + Math.floor((Math.random() - 0.5) * 20)),
      contactCount: Math.max(0, baseMetrics.contactCount + Math.floor((Math.random() - 0.5) * 10)),
      constraintCount: Math.max(0, baseMetrics.constraintCount + Math.floor((Math.random() - 0.5) * 3)),
      islandCount: Math.max(1, baseMetrics.islandCount + Math.floor((Math.random() - 0.5) * 2))
    };
  }, [currentMetrics]);

  /**
   * 检查性能警告
   */
  const checkPerformanceWarnings = useCallback((metrics: PerformanceMetrics) => {
    const newWarnings: PerformanceWarning[] = [];
    
    // FPS警告
    if (metrics.fps < 30) {
      newWarnings.push({
        id: `fps_${Date.now()}`,
        type: 'fps',
        severity: metrics.fps < 20 ? 'high' : 'medium',
        message: `帧率过低: ${metrics.fps.toFixed(1)} FPS`,
        timestamp: Date.now(),
        resolved: false
      });
    }
    
    // 内存警告
    if (metrics.memoryUsage > 80) {
      newWarnings.push({
        id: `memory_${Date.now()}`,
        type: 'memory',
        severity: metrics.memoryUsage > 90 ? 'high' : 'medium',
        message: `内存使用率过高: ${metrics.memoryUsage.toFixed(1)}%`,
        timestamp: Date.now(),
        resolved: false
      });
    }
    
    // 物理时间警告
    if (metrics.physicsTime > 10) {
      newWarnings.push({
        id: `time_${Date.now()}`,
        type: 'time',
        severity: metrics.physicsTime > 15 ? 'high' : 'medium',
        message: `物理计算时间过长: ${metrics.physicsTime.toFixed(1)}ms`,
        timestamp: Date.now(),
        resolved: false
      });
    }
    
    // 对象数量警告
    if (metrics.objectCount > 500) {
      newWarnings.push({
        id: `objects_${Date.now()}`,
        type: 'objects',
        severity: metrics.objectCount > 1000 ? 'high' : 'medium',
        message: `物理对象过多: ${metrics.objectCount} 个`,
        timestamp: Date.now(),
        resolved: false
      });
    }
    
    if (newWarnings.length > 0) {
      setWarnings(prev => [...prev.slice(-10), ...newWarnings]);
    }
  }, []);

  /**
   * 更新性能指标
   */
  const updateMetrics = useCallback(() => {
    const newMetrics = generateMetrics();
    setCurrentMetrics(newMetrics);
    
    // 添加到历史记录
    const historyEntry: PerformanceHistory = {
      timestamp: Date.now(),
      metrics: newMetrics
    };
    
    setHistory(prev => [...prev.slice(-100), historyEntry]);
    
    // 检查警告
    checkPerformanceWarnings(newMetrics);
    
    // 回调
    onMetricsChange?.(newMetrics);
    
    // 自动优化
    if (autoOptimize) {
      performAutoOptimization(newMetrics);
    }
  }, [generateMetrics, checkPerformanceWarnings, onMetricsChange, autoOptimize]);

  /**
   * 自动优化
   */
  const performAutoOptimization = useCallback((metrics: PerformanceMetrics) => {
    // 简单的自动优化逻辑
    if (metrics.fps < 30) {
      console.log('执行自动优化: 降低物理精度');
    }
    if (metrics.memoryUsage > 85) {
      console.log('执行自动优化: 清理内存');
    }
  }, []);

  /**
   * 开始监控
   */
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    intervalRef.current = setInterval(updateMetrics, 1000);
  }, [updateMetrics]);

  /**
   * 停止监控
   */
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  }, []);

  /**
   * 重置数据
   */
  const resetData = useCallback(() => {
    setHistory([]);
    setWarnings([]);
    setCurrentMetrics({
      fps: 60,
      frameTime: 16.67,
      physicsTime: 5.2,
      collisionTime: 2.1,
      integrationTime: 1.8,
      broadPhaseTime: 0.8,
      narrowPhaseTime: 1.3,
      memoryUsage: 45.6,
      objectCount: 150,
      contactCount: 23,
      constraintCount: 8,
      islandCount: 3
    });
  }, []);

  /**
   * 绘制性能图表
   */
  const drawPerformanceChart = useCallback(() => {
    const canvas = chartRef.current;
    if (!canvas || history.length < 2) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;
    
    // 绘制背景网格
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 10; i++) {
      const y = padding + (i / 10) * chartHeight;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }
    
    // 获取选中指标的数据
    const data = history.map(h => {
      switch (selectedMetric) {
        case 'fps': return h.metrics.fps;
        case 'frameTime': return h.metrics.frameTime;
        case 'physicsTime': return h.metrics.physicsTime;
        case 'memoryUsage': return h.metrics.memoryUsage;
        case 'objectCount': return h.metrics.objectCount / 10; // 缩放
        default: return h.metrics.fps;
      }
    });
    
    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue || 1;
    
    // 绘制数据线
    ctx.strokeStyle = '#1890ff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    data.forEach((value, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth;
      const y = padding + chartHeight - ((value - minValue) / range) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
    
    // 绘制坐标轴标签
    ctx.fillStyle = '#666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    
    // Y轴标签
    for (let i = 0; i <= 5; i++) {
      const value = minValue + (range * i / 5);
      const y = padding + chartHeight - (i / 5) * chartHeight;
      ctx.textAlign = 'right';
      ctx.fillText(value.toFixed(1), padding - 10, y + 4);
    }
    
    // 标题
    ctx.textAlign = 'center';
    ctx.font = '14px Arial';
    ctx.fillText(getMetricDisplayName(selectedMetric), width / 2, 20);
  }, [history, selectedMetric]);

  /**
   * 获取指标显示名称
   */
  const getMetricDisplayName = (metric: string): string => {
    const names: { [key: string]: string } = {
      fps: 'FPS',
      frameTime: '帧时间 (ms)',
      physicsTime: '物理时间 (ms)',
      memoryUsage: '内存使用率 (%)',
      objectCount: '对象数量 (x10)'
    };
    return names[metric] || metric;
  };

  /**
   * 获取性能状态颜色
   */
  const getPerformanceStatus = (metrics: PerformanceMetrics) => {
    if (metrics.fps < 30 || metrics.memoryUsage > 80 || metrics.physicsTime > 10) {
      return { status: 'error', text: '性能警告' };
    } else if (metrics.fps < 45 || metrics.memoryUsage > 60 || metrics.physicsTime > 7) {
      return { status: 'warning', text: '性能一般' };
    } else {
      return { status: 'success', text: '性能良好' };
    }
  };

  // 绘制图表
  useEffect(() => {
    drawPerformanceChart();
  }, [drawPerformanceChart]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const performanceStatus = getPerformanceStatus(currentMetrics);

  /**
   * 渲染实时指标
   */
  const renderRealTimeMetrics = () => (
    <Card title="实时性能指标" size="small">
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title="FPS"
            value={currentMetrics.fps}
            precision={1}
            valueStyle={{ color: currentMetrics.fps < 30 ? '#ff4d4f' : '#3f8600' }}
            suffix="fps"
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="帧时间"
            value={currentMetrics.frameTime}
            precision={2}
            valueStyle={{ color: currentMetrics.frameTime > 33 ? '#ff4d4f' : '#3f8600' }}
            suffix="ms"
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="物理时间"
            value={currentMetrics.physicsTime}
            precision={1}
            valueStyle={{ color: currentMetrics.physicsTime > 10 ? '#ff4d4f' : '#3f8600' }}
            suffix="ms"
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="内存使用"
            value={currentMetrics.memoryUsage}
            precision={1}
            valueStyle={{ color: currentMetrics.memoryUsage > 80 ? '#ff4d4f' : '#3f8600' }}
            suffix="%"
          />
        </Col>
      </Row>
      
      <Divider />
      
      <Row gutter={16}>
        <Col span={6}>
          <Text strong>物理对象: </Text>
          <Text>{currentMetrics.objectCount}</Text>
        </Col>
        <Col span={6}>
          <Text strong>接触点: </Text>
          <Text>{currentMetrics.contactCount}</Text>
        </Col>
        <Col span={6}>
          <Text strong>约束: </Text>
          <Text>{currentMetrics.constraintCount}</Text>
        </Col>
        <Col span={6}>
          <Text strong>岛屿: </Text>
          <Text>{currentMetrics.islandCount}</Text>
        </Col>
      </Row>
    </Card>
  );

  /**
   * 渲染性能图表
   */
  const renderPerformanceChart = () => (
    <Card title="性能趋势" size="small">
      <Space style={{ marginBottom: 16 }}>
        <Select
          value={selectedMetric}
          onChange={setSelectedMetric}
          style={{ width: 150 }}
          size="small"
        >
          <Option value="fps">FPS</Option>
          <Option value="frameTime">帧时间</Option>
          <Option value="physicsTime">物理时间</Option>
          <Option value="memoryUsage">内存使用</Option>
          <Option value="objectCount">对象数量</Option>
        </Select>
        
        <Button
          size="small"
          icon={<ReloadOutlined />}
          onClick={resetData}
          disabled={readonly}
        >
          重置数据
        </Button>
      </Space>
      
      <canvas
        ref={chartRef}
        width={600}
        height={300}
        style={{ 
          border: '1px solid #d9d9d9',
          backgroundColor: '#fafafa'
        }}
      />
    </Card>
  );

  /**
   * 渲染警告列表
   */
  const renderWarnings = () => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        width: 120,
        render: (timestamp: number) => new Date(timestamp).toLocaleTimeString()
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 80,
        render: (type: string) => {
          const colors: { [key: string]: string } = {
            fps: 'blue',
            memory: 'orange',
            objects: 'green',
            time: 'red'
          };
          return <Tag color={colors[type]}>{type.toUpperCase()}</Tag>;
        }
      },
      {
        title: '严重程度',
        dataIndex: 'severity',
        key: 'severity',
        width: 100,
        render: (severity: string) => {
          const colors: { [key: string]: string } = {
            low: 'green',
            medium: 'orange',
            high: 'red'
          };
          const icons: { [key: string]: React.ReactNode } = {
            low: <CheckCircleOutlined />,
            medium: <ExclamationCircleOutlined />,
            high: <WarningOutlined />
          };
          return (
            <Tag color={colors[severity]} icon={icons[severity]}>
              {severity}
            </Tag>
          );
        }
      },
      {
        title: '消息',
        dataIndex: 'message',
        key: 'message'
      }
    ];

    return (
      <Card title="性能警告" size="small">
        <Table
          columns={columns}
          dataSource={warnings.slice(-20)}
          rowKey="id"
          size="small"
          pagination={false}
          scroll={{ y: 200 }}
        />
      </Card>
    );
  };

  return (
    <div className="physics-performance-monitor-panel">
      <Card
        title={
          <Space>
            <DashboardOutlined />
            <Title level={4} style={{ margin: 0 }}>
              物理性能监控
            </Title>
            <Tag 
              color={performanceStatus.status === 'success' ? 'green' : 
                     performanceStatus.status === 'warning' ? 'orange' : 'red'}
              icon={performanceStatus.status === 'success' ? <CheckCircleOutlined /> :
                    performanceStatus.status === 'warning' ? <ExclamationCircleOutlined /> :
                    <WarningOutlined />}
            >
              {performanceStatus.text}
            </Tag>
          </Space>
        }
        size="small"
        extra={
          <Space>
            <Switch
              checked={autoOptimize}
              onChange={setAutoOptimize}
              disabled={readonly}
              checkedChildren="自动优化"
              unCheckedChildren="自动优化"
            />
            <Button
              type={isMonitoring ? "default" : "primary"}
              icon={<ThunderboltOutlined />}
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
              disabled={readonly}
            >
              {isMonitoring ? '停止监控' : '开始监控'}
            </Button>
          </Space>
        }
      >
        <Tabs defaultActiveKey="metrics" size="small">
          <TabPane tab="实时指标" key="metrics">
            <Space direction="vertical" style={{ width: '100%' }}>
              {renderRealTimeMetrics()}
              
              <Row gutter={16}>
                <Col span={8}>
                  <Card title="碰撞检测" size="small">
                    <Progress 
                      type="circle" 
                      percent={Math.min((currentMetrics.collisionTime / 5) * 100, 100)}
                      format={() => `${currentMetrics.collisionTime.toFixed(1)}ms`}
                      strokeColor={currentMetrics.collisionTime > 3 ? '#ff4d4f' : '#52c41a'}
                      size={80}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="积分计算" size="small">
                    <Progress 
                      type="circle" 
                      percent={Math.min((currentMetrics.integrationTime / 3) * 100, 100)}
                      format={() => `${currentMetrics.integrationTime.toFixed(1)}ms`}
                      strokeColor={currentMetrics.integrationTime > 2 ? '#ff4d4f' : '#52c41a'}
                      size={80}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="宽相位检测" size="small">
                    <Progress 
                      type="circle" 
                      percent={Math.min((currentMetrics.broadPhaseTime / 2) * 100, 100)}
                      format={() => `${currentMetrics.broadPhaseTime.toFixed(1)}ms`}
                      strokeColor={currentMetrics.broadPhaseTime > 1.5 ? '#ff4d4f' : '#52c41a'}
                      size={80}
                    />
                  </Card>
                </Col>
              </Row>
            </Space>
          </TabPane>
          
          <TabPane tab="性能趋势" key="chart">
            {renderPerformanceChart()}
          </TabPane>
          
          <TabPane tab="警告日志" key="warnings">
            {renderWarnings()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PhysicsPerformanceMonitorPanel;
