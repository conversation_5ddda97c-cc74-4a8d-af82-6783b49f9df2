/**
 * 流体模拟面板组件
 * 为FluidSimulationNode提供流体模拟参数配置和可视化
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Slider,
  Switch,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Tooltip,
  Select,
  InputNumber,
  Divider,
  Alert,
  Progress,
  Tabs
} from 'antd';
import {
  ExperimentOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  DropboxOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 流体参数接口
 */
interface FluidParameters {
  density: number;
  viscosity: number;
  pressure: number;
  temperature: number;
  gravity: { x: number; y: number; z: number };
  timeStep: number;
  particleCount: number;
  smoothingRadius: number;
  restDensity: number;
  gasConstant: number;
  surfaceTension: number;
  damping: number;
}

/**
 * 流体粒子接口
 */
interface FluidParticle {
  id: number;
  position: { x: number; y: number; z: number };
  velocity: { x: number; y: number; z: number };
  density: number;
  pressure: number;
  mass: number;
  color: string;
}

/**
 * 流体模拟状态接口
 */
interface FluidSimulationState {
  isRunning: boolean;
  currentFrame: number;
  totalParticles: number;
  averageDensity: number;
  averagePressure: number;
  kineticEnergy: number;
  potentialEnergy: number;
  fps: number;
}

/**
 * 流体模拟面板属性
 */
interface FluidSimulationPanelProps {
  nodeId?: string;
  onParametersChange?: (parameters: FluidParameters) => void;
  onParticlesChange?: (particles: FluidParticle[]) => void;
  readonly?: boolean;
}

/**
 * 流体模拟面板组件
 */
export const FluidSimulationPanel: React.FC<FluidSimulationPanelProps> = ({
  nodeId,
  onParametersChange,
  onParticlesChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  
  // 状态管理
  const [parameters, setParameters] = useState<FluidParameters>({
    density: 1000,
    viscosity: 0.001,
    pressure: 101325,
    temperature: 293.15,
    gravity: { x: 0, y: -9.81, z: 0 },
    timeStep: 0.016,
    particleCount: 500,
    smoothingRadius: 0.1,
    restDensity: 1000,
    gasConstant: 2000,
    surfaceTension: 0.0728,
    damping: 0.99
  });
  
  const [particles, setParticles] = useState<FluidParticle[]>([]);
  const [state, setState] = useState<FluidSimulationState>({
    isRunning: false,
    currentFrame: 0,
    totalParticles: 0,
    averageDensity: 0,
    averagePressure: 0,
    kineticEnergy: 0,
    potentialEnergy: 0,
    fps: 0
  });
  
  // UI状态
  const [visualizationMode, setVisualizationMode] = useState<'particles' | 'density' | 'velocity' | 'pressure'>('particles');
  const [showVelocityVectors, setShowVelocityVectors] = useState(false);
  const [showPressureField, setShowPressureField] = useState(false);
  const [colorMode, setColorMode] = useState<'default' | 'velocity' | 'pressure' | 'density'>('default');

  /**
   * 初始化流体粒子
   */
  const initializeParticles = useCallback(() => {
    const newParticles: FluidParticle[] = [];
    const gridSize = Math.ceil(Math.sqrt(parameters.particleCount));
    const spacing = 0.05;
    
    for (let i = 0; i < parameters.particleCount; i++) {
      const x = (i % gridSize) * spacing - (gridSize * spacing) / 2;
      const y = Math.floor(i / gridSize) * spacing + 1;
      const z = 0;
      
      newParticles.push({
        id: i,
        position: { x, y, z },
        velocity: { x: 0, y: 0, z: 0 },
        density: parameters.restDensity,
        pressure: 0,
        mass: 1,
        color: '#1890ff'
      });
    }
    
    setParticles(newParticles);
    setState(prev => ({ ...prev, totalParticles: newParticles.length }));
    onParticlesChange?.(newParticles);
  }, [parameters.particleCount, parameters.restDensity, onParticlesChange]);

  /**
   * 更新参数
   */
  const updateParameter = useCallback((parameter: string, value: any) => {
    const newParameters = { ...parameters, [parameter]: value };
    setParameters(newParameters);
    onParametersChange?.(newParameters);
  }, [parameters, onParametersChange]);

  /**
   * 模拟步骤
   */
  const simulationStep = useCallback(() => {
    if (!state.isRunning) return;
    
    // 简化的SPH模拟步骤
    const newParticles = particles.map(particle => {
      // 应用重力
      const newVelocity = {
        x: particle.velocity.x,
        y: particle.velocity.y + parameters.gravity.y * parameters.timeStep,
        z: particle.velocity.z
      };
      
      // 应用阻尼
      newVelocity.x *= parameters.damping;
      newVelocity.y *= parameters.damping;
      newVelocity.z *= parameters.damping;
      
      // 更新位置
      const newPosition = {
        x: particle.position.x + newVelocity.x * parameters.timeStep,
        y: particle.position.y + newVelocity.y * parameters.timeStep,
        z: particle.position.z + newVelocity.z * parameters.timeStep
      };
      
      // 边界碰撞检测
      if (newPosition.x < -2) {
        newPosition.x = -2;
        newVelocity.x *= -0.5;
      }
      if (newPosition.x > 2) {
        newPosition.x = 2;
        newVelocity.x *= -0.5;
      }
      if (newPosition.y < 0) {
        newPosition.y = 0;
        newVelocity.y *= -0.5;
      }
      if (newPosition.y > 3) {
        newPosition.y = 3;
        newVelocity.y *= -0.5;
      }
      
      // 计算密度和压力（简化）
      const density = parameters.restDensity + Math.random() * 100 - 50;
      const pressure = parameters.gasConstant * (density - parameters.restDensity);
      
      // 根据颜色模式设置颜色
      let color = '#1890ff';
      switch (colorMode) {
        case 'velocity':
          const speed = Math.sqrt(newVelocity.x ** 2 + newVelocity.y ** 2 + newVelocity.z ** 2);
          const speedRatio = Math.min(speed / 5, 1);
          color = `hsl(${240 - speedRatio * 240}, 100%, 50%)`;
          break;
        case 'pressure':
          const pressureRatio = Math.min(Math.abs(pressure) / 10000, 1);
          color = pressure > 0 ? `hsl(0, 100%, ${50 + pressureRatio * 50}%)` : `hsl(240, 100%, ${50 + pressureRatio * 50}%)`;
          break;
        case 'density':
          const densityRatio = (density - parameters.restDensity + 100) / 200;
          color = `hsl(${120 * densityRatio}, 100%, 50%)`;
          break;
      }
      
      return {
        ...particle,
        position: newPosition,
        velocity: newVelocity,
        density,
        pressure,
        color
      };
    });
    
    setParticles(newParticles);
    
    // 更新统计信息
    const avgDensity = newParticles.reduce((sum, p) => sum + p.density, 0) / newParticles.length;
    const avgPressure = newParticles.reduce((sum, p) => sum + p.pressure, 0) / newParticles.length;
    const kineticEnergy = newParticles.reduce((sum, p) => {
      const speed = Math.sqrt(p.velocity.x ** 2 + p.velocity.y ** 2 + p.velocity.z ** 2);
      return sum + 0.5 * p.mass * speed ** 2;
    }, 0);
    
    setState(prev => ({
      ...prev,
      currentFrame: prev.currentFrame + 1,
      averageDensity: avgDensity,
      averagePressure: avgPressure,
      kineticEnergy,
      fps: Math.round(1 / parameters.timeStep)
    }));
    
    onParticlesChange?.(newParticles);
  }, [state.isRunning, particles, parameters, colorMode, onParticlesChange]);

  /**
   * 绘制流体可视化
   */
  const drawFluidVisualization = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const scale = 100;
    const offsetX = canvas.width / 2;
    const offsetY = canvas.height - 50;

    // 绘制容器边界
    ctx.strokeStyle = '#d9d9d9';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.rect(offsetX - 2 * scale, offsetY - 3 * scale, 4 * scale, 3 * scale);
    ctx.stroke();

    // 根据可视化模式绘制
    switch (visualizationMode) {
      case 'particles':
        drawParticles(ctx, offsetX, offsetY, scale);
        break;
      case 'density':
        drawDensityField(ctx, offsetX, offsetY, scale);
        break;
      case 'velocity':
        drawVelocityField(ctx, offsetX, offsetY, scale);
        break;
      case 'pressure':
        drawPressureField(ctx, offsetX, offsetY, scale);
        break;
    }

    // 绘制速度向量
    if (showVelocityVectors) {
      drawVelocityVectors(ctx, offsetX, offsetY, scale);
    }
  }, [visualizationMode, showVelocityVectors, particles]);

  /**
   * 绘制粒子
   */
  const drawParticles = (ctx: CanvasRenderingContext2D, offsetX: number, offsetY: number, scale: number) => {
    particles.forEach(particle => {
      const x = offsetX + particle.position.x * scale;
      const y = offsetY - particle.position.y * scale;
      
      ctx.fillStyle = particle.color;
      ctx.beginPath();
      ctx.arc(x, y, 3, 0, 2 * Math.PI);
      ctx.fill();
    });
  };

  /**
   * 绘制密度场
   */
  const drawDensityField = (ctx: CanvasRenderingContext2D, offsetX: number, offsetY: number, scale: number) => {
    const gridSize = 20;
    const cellSize = (4 * scale) / gridSize;
    
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        const x = offsetX - 2 * scale + i * cellSize;
        const y = offsetY - 3 * scale + j * cellSize;
        
        // 计算该网格点的密度（简化）
        const density = Math.random() * 200 + 900;
        const densityRatio = (density - 900) / 200;
        
        ctx.fillStyle = `rgba(0, 100, 255, ${densityRatio * 0.5})`;
        ctx.fillRect(x, y, cellSize, cellSize);
      }
    }
    
    // 在密度场上绘制粒子
    drawParticles(ctx, offsetX, offsetY, scale);
  };

  /**
   * 绘制速度场
   */
  const drawVelocityField = (ctx: CanvasRenderingContext2D, offsetX: number, offsetY: number, scale: number) => {
    particles.forEach(particle => {
      const x = offsetX + particle.position.x * scale;
      const y = offsetY - particle.position.y * scale;
      
      const speed = Math.sqrt(particle.velocity.x ** 2 + particle.velocity.y ** 2);
      const alpha = Math.min(speed / 2, 1);
      
      ctx.fillStyle = `rgba(255, 100, 0, ${alpha})`;
      ctx.beginPath();
      ctx.arc(x, y, 2 + speed * 2, 0, 2 * Math.PI);
      ctx.fill();
    });
  };

  /**
   * 绘制压力场
   */
  const drawPressureField = (ctx: CanvasRenderingContext2D, offsetX: number, offsetY: number, scale: number) => {
    particles.forEach(particle => {
      const x = offsetX + particle.position.x * scale;
      const y = offsetY - particle.position.y * scale;
      
      const pressureRatio = Math.min(Math.abs(particle.pressure) / 5000, 1);
      const color = particle.pressure > 0 ? 
        `rgba(255, 0, 0, ${pressureRatio})` : 
        `rgba(0, 0, 255, ${pressureRatio})`;
      
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(x, y, 3 + pressureRatio * 3, 0, 2 * Math.PI);
      ctx.fill();
    });
  };

  /**
   * 绘制速度向量
   */
  const drawVelocityVectors = (ctx: CanvasRenderingContext2D, offsetX: number, offsetY: number, scale: number) => {
    ctx.strokeStyle = '#fa8c16';
    ctx.lineWidth = 1;
    
    particles.forEach(particle => {
      const x = offsetX + particle.position.x * scale;
      const y = offsetY - particle.position.y * scale;
      
      const vx = particle.velocity.x * scale * 10;
      const vy = -particle.velocity.y * scale * 10;
      
      if (Math.abs(vx) > 1 || Math.abs(vy) > 1) {
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + vx, y + vy);
        ctx.stroke();
        
        // 箭头
        const angle = Math.atan2(vy, vx);
        const arrowLength = 5;
        ctx.beginPath();
        ctx.moveTo(x + vx, y + vy);
        ctx.lineTo(
          x + vx - arrowLength * Math.cos(angle - Math.PI / 6),
          y + vy - arrowLength * Math.sin(angle - Math.PI / 6)
        );
        ctx.moveTo(x + vx, y + vy);
        ctx.lineTo(
          x + vx - arrowLength * Math.cos(angle + Math.PI / 6),
          y + vy - arrowLength * Math.sin(angle + Math.PI / 6)
        );
        ctx.stroke();
      }
    });
  };

  /**
   * 模拟控制
   */
  const startSimulation = useCallback(() => {
    setState(prev => ({ ...prev, isRunning: true }));
  }, []);

  const pauseSimulation = useCallback(() => {
    setState(prev => ({ ...prev, isRunning: false }));
  }, []);

  const resetSimulation = useCallback(() => {
    setState({
      isRunning: false,
      currentFrame: 0,
      totalParticles: 0,
      averageDensity: 0,
      averagePressure: 0,
      kineticEnergy: 0,
      potentialEnergy: 0,
      fps: 0
    });
    initializeParticles();
  }, [initializeParticles]);

  // 初始化
  useEffect(() => {
    initializeParticles();
  }, [initializeParticles]);

  // 动画循环
  useEffect(() => {
    const animate = () => {
      simulationStep();
      drawFluidVisualization();
      
      if (state.isRunning) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };
    
    if (state.isRunning) {
      animate();
    } else {
      drawFluidVisualization();
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state.isRunning, simulationStep, drawFluidVisualization]);

  return (
    <div className="fluid-simulation-panel">
      <Card
        title={
          <Space>
            <DropboxOutlined />
            <Title level={4} style={{ margin: 0 }}>
              流体模拟
            </Title>
            <Tag color={state.isRunning ? 'green' : 'default'}>
              {state.isRunning ? '运行中' : '已停止'}
            </Tag>
          </Space>
        }
        size="small"
      >
        <Tabs defaultActiveKey="visualization" size="small">
          <TabPane tab="可视化" key="visualization">
            <Row gutter={16}>
              <Col span={16}>
                <Card title="流体可视化" size="small">
                  <Space style={{ marginBottom: 8 }}>
                    <Button
                      type={state.isRunning ? "default" : "primary"}
                      icon={<PlayCircleOutlined />}
                      onClick={startSimulation}
                      disabled={readonly || state.isRunning}
                      size="small"
                    >
                      开始
                    </Button>
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={pauseSimulation}
                      disabled={readonly || !state.isRunning}
                      size="small"
                    >
                      暂停
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={resetSimulation}
                      disabled={readonly}
                      size="small"
                    >
                      重置
                    </Button>
                    
                    <Select
                      value={visualizationMode}
                      onChange={setVisualizationMode}
                      size="small"
                    >
                      <Option value="particles">粒子</Option>
                      <Option value="density">密度场</Option>
                      <Option value="velocity">速度场</Option>
                      <Option value="pressure">压力场</Option>
                    </Select>
                    
                    <Select
                      value={colorMode}
                      onChange={setColorMode}
                      size="small"
                    >
                      <Option value="default">默认</Option>
                      <Option value="velocity">速度</Option>
                      <Option value="pressure">压力</Option>
                      <Option value="density">密度</Option>
                    </Select>
                    
                    <Switch
                      size="small"
                      checked={showVelocityVectors}
                      onChange={setShowVelocityVectors}
                      checkedChildren="速度向量"
                      unCheckedChildren="速度向量"
                    />
                  </Space>
                  
                  <canvas
                    ref={canvasRef}
                    width={600}
                    height={400}
                    style={{ 
                      border: '1px solid #d9d9d9',
                      backgroundColor: '#fafafa'
                    }}
                  />
                </Card>
              </Col>
              
              <Col span={8}>
                <Card title="模拟状态" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Text strong>帧数</Text>
                        <div>{state.currentFrame}</div>
                      </Col>
                      <Col span={12}>
                        <Text strong>FPS</Text>
                        <div>{state.fps}</div>
                      </Col>
                    </Row>
                    
                    <Row gutter={16}>
                      <Col span={12}>
                        <Text strong>粒子数</Text>
                        <div>{state.totalParticles}</div>
                      </Col>
                      <Col span={12}>
                        <Text strong>动能</Text>
                        <div>{state.kineticEnergy.toFixed(2)} J</div>
                      </Col>
                    </Row>
                    
                    <Divider />
                    
                    <div>
                      <Text strong>平均密度</Text>
                      <Progress 
                        percent={Math.min((state.averageDensity / 1200) * 100, 100)} 
                        size="small"
                        strokeColor="#52c41a"
                      />
                      <Text type="secondary">{state.averageDensity.toFixed(2)} kg/m³</Text>
                    </div>
                    
                    <div>
                      <Text strong>平均压力</Text>
                      <Progress 
                        percent={Math.min(Math.abs(state.averagePressure) / 10000 * 100, 100)} 
                        size="small"
                        strokeColor={state.averagePressure > 0 ? "#ff4d4f" : "#1890ff"}
                      />
                      <Text type="secondary">{state.averagePressure.toFixed(2)} Pa</Text>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab="参数" key="parameters">
            <Card title="流体参数" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Text strong>密度 (kg/m³)</Text>
                    <InputNumber
                      min={1}
                      max={2000}
                      value={parameters.density}
                      onChange={(value) => updateParameter('density', value || 1000)}
                      disabled={readonly}
                      size="small"
                      style={{ width: '100%' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Text strong>粘度 (Pa·s)</Text>
                    <InputNumber
                      min={0.0001}
                      max={0.1}
                      step={0.0001}
                      value={parameters.viscosity}
                      onChange={(value) => updateParameter('viscosity', value || 0.001)}
                      disabled={readonly}
                      size="small"
                      style={{ width: '100%' }}
                    />
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Text strong>粒子数量</Text>
                    <Slider
                      min={50}
                      max={1000}
                      value={parameters.particleCount}
                      onChange={(value) => updateParameter('particleCount', value)}
                      disabled={readonly}
                    />
                    <Text type="secondary">{parameters.particleCount}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>时间步长</Text>
                    <Slider
                      min={0.001}
                      max={0.1}
                      step={0.001}
                      value={parameters.timeStep}
                      onChange={(value) => updateParameter('timeStep', value)}
                      disabled={readonly}
                    />
                    <Text type="secondary">{parameters.timeStep.toFixed(3)}s</Text>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Text strong>重力 X</Text>
                    <InputNumber
                      min={-20}
                      max={20}
                      step={0.1}
                      value={parameters.gravity.x}
                      onChange={(value) => updateParameter('gravity', {
                        ...parameters.gravity,
                        x: value || 0
                      })}
                      disabled={readonly}
                      size="small"
                      style={{ width: '100%' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Text strong>重力 Y</Text>
                    <InputNumber
                      min={-20}
                      max={20}
                      step={0.1}
                      value={parameters.gravity.y}
                      onChange={(value) => updateParameter('gravity', {
                        ...parameters.gravity,
                        y: value || -9.81
                      })}
                      disabled={readonly}
                      size="small"
                      style={{ width: '100%' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Text strong>重力 Z</Text>
                    <InputNumber
                      min={-20}
                      max={20}
                      step={0.1}
                      value={parameters.gravity.z}
                      onChange={(value) => updateParameter('gravity', {
                        ...parameters.gravity,
                        z: value || 0
                      })}
                      disabled={readonly}
                      size="small"
                      style={{ width: '100%' }}
                    />
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Text strong>平滑半径</Text>
                    <Slider
                      min={0.01}
                      max={0.5}
                      step={0.01}
                      value={parameters.smoothingRadius}
                      onChange={(value) => updateParameter('smoothingRadius', value)}
                      disabled={readonly}
                    />
                    <Text type="secondary">{parameters.smoothingRadius.toFixed(2)}m</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>阻尼系数</Text>
                    <Slider
                      min={0.9}
                      max={1}
                      step={0.001}
                      value={parameters.damping}
                      onChange={(value) => updateParameter('damping', value)}
                      disabled={readonly}
                    />
                    <Text type="secondary">{(parameters.damping * 100).toFixed(1)}%</Text>
                  </Col>
                </Row>
              </Space>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default FluidSimulationPanel;
