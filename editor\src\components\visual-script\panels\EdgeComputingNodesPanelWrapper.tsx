/**
 * 边缘计算节点面板包装器
 * 批次0.2：集成46个边缘计算节点到编辑器
 */

import React, { useState, useEffect, useRef } from 'react';
import { message } from 'antd';
import EdgeComputingNodesPanel from './EdgeComputingNodesPanel';
import { EdgeComputingNodesIntegration } from '../nodes/EdgeComputingNodesIntegration';
import { NodeEditor } from '../NodeEditor';

/**
 * 边缘计算节点面板包装器属性
 */
export interface EdgeComputingNodesPanelWrapperProps {
  onNodeSelect?: (nodeType: string) => void;
  onNodeAdd?: (nodeType: string) => void;
  visible?: boolean;
}

/**
 * 创建模拟节点编辑器
 */
const createMockNodeEditor = (): NodeEditor => {
  return {
    registerNodeType: (nodeConfig: any) => {
      console.log(`注册节点类型: ${nodeConfig.type}`);
    },
    addNodeCategory: (category: any) => {
      console.log(`添加节点分类: ${category.name}`);
    },
    addNodeToPalette: (nodeType: string, category: string) => {
      console.log(`添加节点到调色板: ${nodeType} -> ${category}`);
    },
    refreshNodePalette: () => {
      console.log('刷新节点调色板');
    },
    createNode: (nodeType: string, position: { x: number; y: number }) => {
      console.log(`创建节点: ${nodeType} at (${position.x}, ${position.y})`);
      return {
        id: `node_${Date.now()}`,
        type: nodeType,
        position,
        data: {}
      };
    },
    addNode: (node: any) => {
      console.log(`添加节点到画布: ${node.id}`);
    },
    selectNode: (nodeId: string) => {
      console.log(`选择节点: ${nodeId}`);
    },
    getSelectedNodes: () => {
      return [];
    },
    deleteNode: (nodeId: string) => {
      console.log(`删除节点: ${nodeId}`);
    },
    connectNodes: (sourceId: string, targetId: string) => {
      console.log(`连接节点: ${sourceId} -> ${targetId}`);
    },
    disconnectNodes: (sourceId: string, targetId: string) => {
      console.log(`断开节点连接: ${sourceId} -> ${targetId}`);
    },
    getNodeTypes: () => {
      return [];
    },
    getNodeCategories: () => {
      return [];
    },
    exportGraph: () => {
      return { nodes: [], connections: [] };
    },
    importGraph: (graph: any) => {
      console.log('导入图形:', graph);
    },
    clearGraph: () => {
      console.log('清空图形');
    },
    undo: () => {
      console.log('撤销操作');
    },
    redo: () => {
      console.log('重做操作');
    },
    zoomIn: () => {
      console.log('放大');
    },
    zoomOut: () => {
      console.log('缩小');
    },
    fitToScreen: () => {
      console.log('适应屏幕');
    },
    centerView: () => {
      console.log('居中视图');
    }
  } as NodeEditor;
};

export const EdgeComputingNodesPanelWrapper: React.FC<EdgeComputingNodesPanelWrapperProps> = ({
  onNodeSelect,
  onNodeAdd,
  visible = true
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [integration, setIntegration] = useState<EdgeComputingNodesIntegration | null>(null);
  const nodeEditorRef = useRef<NodeEditor | null>(null);

  // 初始化边缘计算节点集成
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        setLoading(true);
        setError(null);

        // 创建模拟节点编辑器
        const mockNodeEditor = createMockNodeEditor();
        nodeEditorRef.current = mockNodeEditor;

        // 创建边缘计算节点集成
        const edgeComputingIntegration = new EdgeComputingNodesIntegration(mockNodeEditor);
        
        // 集成所有边缘计算节点
        edgeComputingIntegration.integrateAllNodes();
        
        setIntegration(edgeComputingIntegration);
        
        message.success(`边缘计算节点集成成功！已集成 ${edgeComputingIntegration.getRegisteredNodeCount()} 个节点`);
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '未知错误';
        setError(errorMessage);
        message.error(`边缘计算节点集成失败: ${errorMessage}`);
        console.error('边缘计算节点集成失败:', err);
      } finally {
        setLoading(false);
      }
    };

    if (visible) {
      initializeIntegration();
    }
  }, [visible]);

  // 处理节点选择
  const handleNodeSelect = (nodeType: string) => {
    try {
      console.log(`选择边缘计算节点: ${nodeType}`);
      
      if (integration) {
        const nodeConfig = integration.getNodeConfig(nodeType);
        if (nodeConfig) {
          console.log('节点配置:', nodeConfig);
        }
      }
      
      if (onNodeSelect) {
        onNodeSelect(nodeType);
      }
      
      message.info(`已选择节点: ${nodeType}`);
    } catch (err) {
      console.error('节点选择失败:', err);
      message.error('节点选择失败');
    }
  };

  // 处理节点添加
  const handleNodeAdd = (nodeType: string) => {
    try {
      console.log(`添加边缘计算节点: ${nodeType}`);
      
      if (nodeEditorRef.current) {
        // 在随机位置创建节点
        const position = {
          x: Math.random() * 400 + 100,
          y: Math.random() * 300 + 100
        };
        
        const node = nodeEditorRef.current.createNode(nodeType, position);
        if (node) {
          nodeEditorRef.current.addNode(node);
          nodeEditorRef.current.selectNode(node.id);
        }
      }
      
      if (onNodeAdd) {
        onNodeAdd(nodeType);
      }
      
      message.success(`已添加节点: ${nodeType}`);
    } catch (err) {
      console.error('节点添加失败:', err);
      message.error('节点添加失败');
    }
  };

  // 如果不可见，返回null
  if (!visible) {
    return null;
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center', 
        color: '#ff4d4f',
        border: '1px solid #ffccc7',
        borderRadius: '6px',
        backgroundColor: '#fff2f0'
      }}>
        <h4>边缘计算节点加载失败</h4>
        <p>{error}</p>
        <button 
          onClick={() => window.location.reload()}
          style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重新加载
        </button>
      </div>
    );
  }

  return (
    <EdgeComputingNodesPanel
      onNodeSelect={handleNodeSelect}
      onNodeAdd={handleNodeAdd}
      visible={visible}
      loading={loading}
    />
  );
};

export default EdgeComputingNodesPanelWrapper;
