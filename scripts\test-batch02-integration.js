/**
 * 批次0.2节点集成测试脚本
 * 简单验证批次0.2节点的注册和集成状态
 */

const fs = require('fs');
const path = require('path');

/**
 * 验证结果
 */
const results = {
  totalNodes: 60,
  expectedNodes: {
    inputSystem: 25,
    animationExtension: 15,
    audioExtension: 9,
    physicsExtension: 11
  },
  foundNodes: {
    inputSystem: 0,
    animationExtension: 0,
    audioExtension: 0,
    physicsExtension: 0
  },
  errors: [],
  warnings: []
};

/**
 * 预期的节点类型
 */
const expectedNodeTypes = {
  inputSystem: [
    'MultiTouchNode', 'GestureRecognitionNode', 'VoiceInputNode', 'MotionSensorNode', 'AccelerometerNode',
    'GyroscopeNode', 'CompassNode', 'ProximityNode', 'LightSensorNode', 'PressureSensorNode',
    'VRControllerInputNode', 'VRHeadsetTrackingNode', 'ARTouchInputNode', 'ARGestureInputNode', 'SpatialInputNode',
    'EyeTrackingInputNode', 'HandTrackingInputNode', 'VoiceCommandInputNode', 'GamepadInputNode', 'KeyboardInputNode',
    'MouseInputNode', 'TouchInputNode', 'PenInputNode', 'MIDIInputNode', 'CustomInputNode'
  ],
  animationExtension: [
    'AnimationTimelineNode', 'AnimationBlendTreeNode', 'AnimationStateMachineNode', 'IKSystemNode', 'AnimationRetargetingNode',
    'AnimationCompressionNode', 'AnimationOptimizationNode', 'AnimationBakingNode', 'AnimationExportNode', 'AnimationImportNode',
    'AnimationValidationNode', 'AnimationLayerNode', 'AnimationBlendingNode', 'AnimationCurveNode', 'KeyframeEditorNode'
  ],
  audioExtension: [
    'AudioMixerNode', 'AudioEffectChainNode', 'AudioReverbNode', 'AudioEQNode', 'AudioCompressorNode',
    'AudioDelayNode', 'AudioChorusNode', 'AudioDistortionNode', 'AudioOptimizationNode'
  ],
  physicsExtension: [
    'SoftBodyPhysicsNode', 'FluidSimulationNode', 'ClothSimulationNode', 'RopeSimulationNode', 'DestructionNode',
    'PhysicsConstraintNode', 'PhysicsJointNode', 'PhysicsMotorNode', 'PhysicsOptimizationNode', 'PhysicsLODNode',
    'PhysicsPerformanceMonitorNode'
  ]
};

/**
 * 验证节点注册
 */
function validateNodeRegistration() {
  console.log('验证节点注册状态...');

  try {
    // 检查批次0.2注册表文件
    const registryPath = path.resolve('engine/src/visual-script/registry/Batch02NodesRegistry.ts');
    if (!fs.existsSync(registryPath)) {
      results.errors.push('Batch02NodesRegistry.ts 文件不存在');
      return;
    }

    const registryContent = fs.readFileSync(registryPath, 'utf8');

    // 验证注册方法
    const requiredMethods = [
      'registerOtherSystemIntegrationNodes',
      'registerInputSystemNodes',
      'registerAnimationExtensionNodes',
      'registerAudioExtensionNodes',
      'registerPhysicsExtensionNodes'
    ];

    requiredMethods.forEach(method => {
      if (!registryContent.includes(method)) {
        results.errors.push(`缺少注册方法: ${method}`);
      }
    });

    // 验证节点类型
    Object.entries(expectedNodeTypes).forEach(([category, nodeTypes]) => {
      nodeTypes.forEach(nodeType => {
        if (registryContent.includes(nodeType)) {
          results.foundNodes[category]++;
        } else {
          results.warnings.push(`节点类型未在注册表中找到: ${nodeType}`);
        }
      });
    });

    console.log('✓ 节点注册验证完成');

  } catch (error) {
    results.errors.push(`节点注册验证失败: ${error.message}`);
  }
}

/**
 * 验证编辑器集成
 */
function validateEditorIntegration() {
  console.log('验证编辑器集成状态...');

  try {
    // 检查编辑器集成文件
    const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch02OtherSystemsIntegration.ts');
    if (!fs.existsSync(integrationPath)) {
      results.errors.push('Batch02OtherSystemsIntegration.ts 文件不存在');
      return;
    }

    const integrationContent = fs.readFileSync(integrationPath, 'utf8');

    // 验证集成方法
    const requiredIntegrationMethods = [
      'integrateAllNodes',
      'integrateInputSystemNodes',
      'integrateAnimationExtensionNodes',
      'integrateAudioExtensionNodes',
      'integratePhysicsExtensionNodes'
    ];

    requiredIntegrationMethods.forEach(method => {
      if (!integrationContent.includes(method)) {
        results.errors.push(`缺少编辑器集成方法: ${method}`);
      }
    });

    // 验证节点分类
    const expectedCategories = [
      'Input/Advanced',
      'Input/VR_AR',
      'Input/Basic',
      'Animation/Extension',
      'Audio/Extension',
      'Physics/Extension'
    ];

    expectedCategories.forEach(category => {
      if (!integrationContent.includes(category)) {
        results.warnings.push(`节点分类未找到: ${category}`);
      }
    });

    console.log('✓ 编辑器集成验证完成');

  } catch (error) {
    results.errors.push(`编辑器集成验证失败: ${error.message}`);
  }
}

/**
 * 验证文档
 */
function validateDocumentation() {
  console.log('验证文档完整性...');

  try {
    // 检查完成报告
    const reportPath = path.resolve('docs/批次0.2其他系统集成节点完成报告.md');
    if (fs.existsSync(reportPath)) {
      console.log('✓ 完成报告文档存在');
    } else {
      results.warnings.push('完成报告文档不存在');
    }

    // 检查开发计划文档
    const planPath = path.resolve('docs/DL引擎视觉脚本系统节点开发计划.md');
    if (fs.existsSync(planPath)) {
      const planContent = fs.readFileSync(planPath, 'utf8');
      if (planContent.includes('批次0.2') && planContent.includes('其他系统集成')) {
        console.log('✓ 开发计划文档包含批次0.2信息');
      } else {
        results.warnings.push('开发计划文档缺少批次0.2信息');
      }
    } else {
      results.warnings.push('开发计划文档不存在');
    }

    console.log('✓ 文档验证完成');

  } catch (error) {
    results.errors.push(`文档验证失败: ${error.message}`);
  }
}

/**
 * 打印结果
 */
function printResults() {
  console.log('\n=== 批次0.2其他系统集成节点验证结果 ===');
  console.log(`总节点数: ${results.totalNodes}`);
  
  console.log('\n节点发现统计:');
  Object.entries(results.foundNodes).forEach(([category, count]) => {
    const expected = results.expectedNodes[category];
    const percentage = ((count / expected) * 100).toFixed(1);
    console.log(`  ${category}: ${count}/${expected} (${percentage}%)`);
  });

  const totalFound = Object.values(results.foundNodes).reduce((sum, count) => sum + count, 0);
  const totalPercentage = ((totalFound / results.totalNodes) * 100).toFixed(1);
  console.log(`\n总体完成率: ${totalFound}/${results.totalNodes} (${totalPercentage}%)`);

  if (results.errors.length > 0) {
    console.log('\n❌ 错误:');
    results.errors.forEach(error => console.log(`  - ${error}`));
  }

  if (results.warnings.length > 0) {
    console.log('\n⚠️  警告:');
    results.warnings.forEach(warning => console.log(`  - ${warning}`));
  }

  if (results.errors.length === 0) {
    console.log('\n✅ 验证通过！批次0.2其他系统集成节点已成功注册和集成');
  } else {
    console.log('\n❌ 验证失败！请修复上述错误后重新验证');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('开始验证批次0.2其他系统集成节点...');

  validateNodeRegistration();
  validateEditorIntegration();
  validateDocumentation();

  printResults();

  // 返回验证结果
  process.exit(results.errors.length > 0 ? 1 : 0);
}

// 运行验证
main();
