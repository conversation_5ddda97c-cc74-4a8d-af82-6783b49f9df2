/**
 * 3D空间音频可视化组件
 * 为SpatialAudioNode提供3D音频定位可视化
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Slider,
  Switch,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Tooltip,
  Select,
  InputNumber,
  Divider,
  Alert
} from 'antd';
import {
  SoundOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 3D音频源接口
 */
interface AudioSource3D {
  id: string;
  name: string;
  position: { x: number; y: number; z: number };
  volume: number;
  pitch: number;
  rolloffFactor: number;
  maxDistance: number;
  referenceDistance: number;
  enabled: boolean;
  playing: boolean;
  color: string;
}

/**
 * 3D音频监听器接口
 */
interface AudioListener3D {
  position: { x: number; y: number; z: number };
  orientation: { forward: { x: number; y: number; z: number }; up: { x: number; y: number; z: number } };
  velocity: { x: number; y: number; z: number };
}

/**
 * 空间音频设置接口
 */
interface SpatialAudioSettings {
  dopplerFactor: number;
  speedOfSound: number;
  distanceModel: 'linear' | 'inverse' | 'exponential';
  panningModel: 'HRTF' | 'equalpower';
  enableOcclusion: boolean;
  enableReverb: boolean;
  roomSize: number;
  reverbDecay: number;
}

/**
 * 3D空间音频可视化属性
 */
interface SpatialAudioVisualizationProps {
  nodeId?: string;
  onSourceChange?: (sources: AudioSource3D[]) => void;
  onListenerChange?: (listener: AudioListener3D) => void;
  onSettingsChange?: (settings: SpatialAudioSettings) => void;
  readonly?: boolean;
}

/**
 * 3D空间音频可视化组件
 */
export const SpatialAudioVisualization: React.FC<SpatialAudioVisualizationProps> = ({
  nodeId,
  onSourceChange,
  onListenerChange,
  onSettingsChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  
  // 状态管理
  const [audioSources, setAudioSources] = useState<AudioSource3D[]>([]);
  const [listener, setListener] = useState<AudioListener3D>({
    position: { x: 0, y: 0, z: 0 },
    orientation: { 
      forward: { x: 0, y: 0, z: -1 }, 
      up: { x: 0, y: 1, z: 0 } 
    },
    velocity: { x: 0, y: 0, z: 0 }
  });
  const [settings, setSettings] = useState<SpatialAudioSettings>({
    dopplerFactor: 1.0,
    speedOfSound: 343.3,
    distanceModel: 'inverse',
    panningModel: 'HRTF',
    enableOcclusion: true,
    enableReverb: true,
    roomSize: 10,
    reverbDecay: 2.0
  });
  
  // UI状态
  const [selectedSource, setSelectedSource] = useState<string>('');
  const [viewMode, setViewMode] = useState<'top' | 'side' | '3d'>('top');
  const [showDistanceCircles, setShowDistanceCircles] = useState(true);
  const [showVelocityVectors, setShowVelocityVectors] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [scale, setScale] = useState(50); // 像素/米

  /**
   * 初始化默认音频源
   */
  useEffect(() => {
    const defaultSources: AudioSource3D[] = [
      {
        id: 'source1',
        name: '音乐播放器',
        position: { x: -3, y: 0, z: -2 },
        volume: 0.8,
        pitch: 1.0,
        rolloffFactor: 1.0,
        maxDistance: 20,
        referenceDistance: 1,
        enabled: true,
        playing: false,
        color: '#ff4d4f'
      },
      {
        id: 'source2',
        name: '环境音效',
        position: { x: 2, y: 1, z: -1 },
        volume: 0.6,
        pitch: 1.0,
        rolloffFactor: 0.5,
        maxDistance: 15,
        referenceDistance: 2,
        enabled: true,
        playing: false,
        color: '#52c41a'
      },
      {
        id: 'source3',
        name: '语音对话',
        position: { x: 0, y: 0, z: 3 },
        volume: 1.0,
        pitch: 1.0,
        rolloffFactor: 2.0,
        maxDistance: 10,
        referenceDistance: 0.5,
        enabled: true,
        playing: false,
        color: '#1890ff'
      }
    ];

    setAudioSources(defaultSources);
  }, []);

  /**
   * 绘制3D场景
   */
  const draw3DScene = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // 绘制网格
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    for (let i = -10; i <= 10; i++) {
      const x = centerX + i * scale;
      const y = centerY + i * scale;
      
      if (x >= 0 && x <= canvas.width) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }
      
      if (y >= 0 && y <= canvas.height) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }
    }

    // 绘制坐标轴
    ctx.strokeStyle = '#d9d9d9';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(centerX, 0);
    ctx.lineTo(centerX, canvas.height);
    ctx.moveTo(0, centerY);
    ctx.lineTo(canvas.width, centerY);
    ctx.stroke();

    // 绘制监听器
    const listenerX = centerX + listener.position.x * scale;
    const listenerY = centerY - (viewMode === 'top' ? listener.position.z : listener.position.y) * scale;
    
    ctx.fillStyle = '#722ed1';
    ctx.beginPath();
    ctx.arc(listenerX, listenerY, 8, 0, 2 * Math.PI);
    ctx.fill();
    
    // 绘制监听器方向
    const forwardX = listener.orientation.forward.x;
    const forwardZ = viewMode === 'top' ? listener.orientation.forward.z : listener.orientation.forward.y;
    ctx.strokeStyle = '#722ed1';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(listenerX, listenerY);
    ctx.lineTo(listenerX + forwardX * 30, listenerY - forwardZ * 30);
    ctx.stroke();

    // 绘制音频源
    audioSources.forEach(source => {
      const sourceX = centerX + source.position.x * scale;
      const sourceY = centerY - (viewMode === 'top' ? source.position.z : source.position.y) * scale;
      
      // 绘制距离圆圈
      if (showDistanceCircles) {
        ctx.strokeStyle = source.color + '40';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        
        // 参考距离圆圈
        ctx.beginPath();
        ctx.arc(sourceX, sourceY, source.referenceDistance * scale, 0, 2 * Math.PI);
        ctx.stroke();
        
        // 最大距离圆圈
        ctx.beginPath();
        ctx.arc(sourceX, sourceY, source.maxDistance * scale, 0, 2 * Math.PI);
        ctx.stroke();
        
        ctx.setLineDash([]);
      }
      
      // 绘制音频源
      ctx.fillStyle = source.enabled ? source.color : '#d9d9d9';
      ctx.beginPath();
      ctx.arc(sourceX, sourceY, source.playing ? 12 : 8, 0, 2 * Math.PI);
      ctx.fill();
      
      // 绘制音频源边框
      ctx.strokeStyle = selectedSource === source.id ? '#000' : source.color;
      ctx.lineWidth = selectedSource === source.id ? 3 : 2;
      ctx.beginPath();
      ctx.arc(sourceX, sourceY, source.playing ? 12 : 8, 0, 2 * Math.PI);
      ctx.stroke();
      
      // 绘制音频源名称
      ctx.fillStyle = '#000';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(source.name, sourceX, sourceY - 20);
      
      // 绘制音量指示
      if (source.playing) {
        const distance = Math.sqrt(
          Math.pow(source.position.x - listener.position.x, 2) +
          Math.pow(source.position.y - listener.position.y, 2) +
          Math.pow(source.position.z - listener.position.z, 2)
        );
        
        const volume = Math.max(0, Math.min(1, source.volume * (source.referenceDistance / Math.max(distance, source.referenceDistance))));
        
        ctx.fillStyle = source.color + '80';
        ctx.beginPath();
        ctx.arc(sourceX, sourceY, 8 + volume * 20, 0, 2 * Math.PI);
        ctx.fill();
      }
    });

    // 绘制连接线（从监听器到选中的音频源）
    if (selectedSource) {
      const source = audioSources.find(s => s.id === selectedSource);
      if (source) {
        const sourceX = centerX + source.position.x * scale;
        const sourceY = centerY - (viewMode === 'top' ? source.position.z : source.position.y) * scale;
        
        ctx.strokeStyle = source.color + '80';
        ctx.lineWidth = 2;
        ctx.setLineDash([10, 5]);
        ctx.beginPath();
        ctx.moveTo(listenerX, listenerY);
        ctx.lineTo(sourceX, sourceY);
        ctx.stroke();
        ctx.setLineDash([]);
        
        // 显示距离
        const distance = Math.sqrt(
          Math.pow(source.position.x - listener.position.x, 2) +
          Math.pow(source.position.y - listener.position.y, 2) +
          Math.pow(source.position.z - listener.position.z, 2)
        );
        
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(
          `${distance.toFixed(2)}m`,
          (listenerX + sourceX) / 2,
          (listenerY + sourceY) / 2 - 10
        );
      }
    }

    // 绘制图例
    ctx.fillStyle = '#000';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('监听器', 10, 30);
    ctx.fillStyle = '#722ed1';
    ctx.beginPath();
    ctx.arc(80, 25, 6, 0, 2 * Math.PI);
    ctx.fill();

  }, [audioSources, listener, selectedSource, viewMode, showDistanceCircles, scale]);

  /**
   * 动画循环
   */
  useEffect(() => {
    const animate = () => {
      draw3DScene();
      if (isPlaying) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };
    
    if (isPlaying) {
      animate();
    } else {
      draw3DScene();
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, draw3DScene]);

  /**
   * 更新音频源参数
   */
  const updateSourceParameter = useCallback((sourceId: string, parameter: string, value: any) => {
    const newSources = audioSources.map(source => 
      source.id === sourceId ? { ...source, [parameter]: value } : source
    );
    setAudioSources(newSources);
    onSourceChange?.(newSources);
  }, [audioSources, onSourceChange]);

  /**
   * 更新监听器参数
   */
  const updateListenerParameter = useCallback((parameter: string, value: any) => {
    const newListener = { ...listener, [parameter]: value };
    setListener(newListener);
    onListenerChange?.(newListener);
  }, [listener, onListenerChange]);

  /**
   * 更新设置
   */
  const updateSettings = useCallback((parameter: string, value: any) => {
    const newSettings = { ...settings, [parameter]: value };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);
  }, [settings, onSettingsChange]);

  /**
   * 渲染音频源控制
   */
  const renderSourceControls = () => {
    const selectedSourceData = audioSources.find(s => s.id === selectedSource);
    
    if (!selectedSourceData) {
      return (
        <Alert
          message="请在3D视图中选择一个音频源"
          type="info"
          showIcon
        />
      );
    }

    return (
      <Card title={`${selectedSourceData.name} - 控制`} size="small">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row gutter={16}>
            <Col span={8}>
              <Text strong>位置 X</Text>
              <InputNumber
                value={selectedSourceData.position.x}
                step={0.1}
                onChange={(value) => updateSourceParameter(selectedSource, 'position', {
                  ...selectedSourceData.position,
                  x: value || 0
                })}
                disabled={readonly}
                size="small"
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <Text strong>位置 Y</Text>
              <InputNumber
                value={selectedSourceData.position.y}
                step={0.1}
                onChange={(value) => updateSourceParameter(selectedSource, 'position', {
                  ...selectedSourceData.position,
                  y: value || 0
                })}
                disabled={readonly}
                size="small"
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={8}>
              <Text strong>位置 Z</Text>
              <InputNumber
                value={selectedSourceData.position.z}
                step={0.1}
                onChange={(value) => updateSourceParameter(selectedSource, 'position', {
                  ...selectedSourceData.position,
                  z: value || 0
                })}
                disabled={readonly}
                size="small"
                style={{ width: '100%' }}
              />
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Text strong>音量</Text>
              <Slider
                min={0}
                max={1}
                step={0.01}
                value={selectedSourceData.volume}
                onChange={(value) => updateSourceParameter(selectedSource, 'volume', value)}
                disabled={readonly}
              />
            </Col>
            <Col span={12}>
              <Text strong>音调</Text>
              <Slider
                min={0.5}
                max={2}
                step={0.01}
                value={selectedSourceData.pitch}
                onChange={(value) => updateSourceParameter(selectedSource, 'pitch', value)}
                disabled={readonly}
              />
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Text strong>衰减因子</Text>
              <Slider
                min={0}
                max={5}
                step={0.1}
                value={selectedSourceData.rolloffFactor}
                onChange={(value) => updateSourceParameter(selectedSource, 'rolloffFactor', value)}
                disabled={readonly}
              />
            </Col>
            <Col span={12}>
              <Text strong>最大距离</Text>
              <InputNumber
                min={1}
                max={100}
                value={selectedSourceData.maxDistance}
                onChange={(value) => updateSourceParameter(selectedSource, 'maxDistance', value || 20)}
                disabled={readonly}
                size="small"
                style={{ width: '100%' }}
              />
            </Col>
          </Row>

          <Row align="middle" justify="space-between">
            <Col>
              <Text strong>播放</Text>
            </Col>
            <Col>
              <Switch
                checked={selectedSourceData.playing}
                onChange={(checked) => updateSourceParameter(selectedSource, 'playing', checked)}
                disabled={readonly}
              />
            </Col>
          </Row>
        </Space>
      </Card>
    );
  };

  /**
   * 渲染监听器控制
   */
  const renderListenerControls = () => (
    <Card title="监听器控制" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>位置 X</Text>
            <InputNumber
              value={listener.position.x}
              step={0.1}
              onChange={(value) => updateListenerParameter('position', {
                ...listener.position,
                x: value || 0
              })}
              disabled={readonly}
              size="small"
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Text strong>位置 Y</Text>
            <InputNumber
              value={listener.position.y}
              step={0.1}
              onChange={(value) => updateListenerParameter('position', {
                ...listener.position,
                y: value || 0
              })}
              disabled={readonly}
              size="small"
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Text strong>位置 Z</Text>
            <InputNumber
              value={listener.position.z}
              step={0.1}
              onChange={(value) => updateListenerParameter('position', {
                ...listener.position,
                z: value || 0
              })}
              disabled={readonly}
              size="small"
              style={{ width: '100%' }}
            />
          </Col>
        </Row>
      </Space>
    </Card>
  );

  /**
   * 渲染设置面板
   */
  const renderSettings = () => (
    <Card title="空间音频设置" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>多普勒因子</Text>
            <Slider
              min={0}
              max={2}
              step={0.1}
              value={settings.dopplerFactor}
              onChange={(value) => updateSettings('dopplerFactor', value)}
              disabled={readonly}
            />
          </Col>
          <Col span={12}>
            <Text strong>声速 (m/s)</Text>
            <InputNumber
              min={100}
              max={500}
              value={settings.speedOfSound}
              onChange={(value) => updateSettings('speedOfSound', value || 343.3)}
              disabled={readonly}
              size="small"
              style={{ width: '100%' }}
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Text strong>距离模型</Text>
            <Select
              value={settings.distanceModel}
              onChange={(value) => updateSettings('distanceModel', value)}
              disabled={readonly}
              style={{ width: '100%' }}
              size="small"
            >
              <Option value="linear">线性</Option>
              <Option value="inverse">反比</Option>
              <Option value="exponential">指数</Option>
            </Select>
          </Col>
          <Col span={12}>
            <Text strong>声像模型</Text>
            <Select
              value={settings.panningModel}
              onChange={(value) => updateSettings('panningModel', value)}
              disabled={readonly}
              style={{ width: '100%' }}
              size="small"
            >
              <Option value="HRTF">HRTF</Option>
              <Option value="equalpower">等功率</Option>
            </Select>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Row align="middle" justify="space-between">
              <Col>
                <Text strong>启用遮挡</Text>
              </Col>
              <Col>
                <Switch
                  checked={settings.enableOcclusion}
                  onChange={(checked) => updateSettings('enableOcclusion', checked)}
                  disabled={readonly}
                />
              </Col>
            </Row>
          </Col>
          <Col span={12}>
            <Row align="middle" justify="space-between">
              <Col>
                <Text strong>启用混响</Text>
              </Col>
              <Col>
                <Switch
                  checked={settings.enableReverb}
                  onChange={(checked) => updateSettings('enableReverb', checked)}
                  disabled={readonly}
                />
              </Col>
            </Row>
          </Col>
        </Row>

        {settings.enableReverb && (
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>房间大小</Text>
              <Slider
                min={1}
                max={50}
                value={settings.roomSize}
                onChange={(value) => updateSettings('roomSize', value)}
                disabled={readonly}
              />
            </Col>
            <Col span={12}>
              <Text strong>混响衰减</Text>
              <Slider
                min={0.1}
                max={10}
                step={0.1}
                value={settings.reverbDecay}
                onChange={(value) => updateSettings('reverbDecay', value)}
                disabled={readonly}
              />
            </Col>
          </Row>
        )}
      </Space>
    </Card>
  );

  return (
    <div className="spatial-audio-visualization">
      <Card
        title={
          <Space>
            <EnvironmentOutlined />
            <Title level={4} style={{ margin: 0 }}>
              3D空间音频可视化
            </Title>
          </Space>
        }
        size="small"
      >
        <Row gutter={16}>
          <Col span={16}>
            <Card title="3D视图" size="small">
              <Space style={{ marginBottom: 8 }}>
                <Select
                  value={viewMode}
                  onChange={setViewMode}
                  size="small"
                >
                  <Option value="top">俯视图</Option>
                  <Option value="side">侧视图</Option>
                </Select>
                
                <Button
                  size="small"
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={() => setIsPlaying(!isPlaying)}
                  disabled={readonly}
                >
                  {isPlaying ? '暂停' : '播放'}
                </Button>
                
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => draw3DScene()}
                >
                  刷新
                </Button>
                
                <Switch
                  size="small"
                  checked={showDistanceCircles}
                  onChange={setShowDistanceCircles}
                  checkedChildren="距离圆"
                  unCheckedChildren="距离圆"
                />
                
                <Text>缩放:</Text>
                <Slider
                  style={{ width: 100 }}
                  min={10}
                  max={100}
                  value={scale}
                  onChange={setScale}
                  size="small"
                />
              </Space>
              
              <canvas
                ref={canvasRef}
                width={600}
                height={400}
                style={{ 
                  border: '1px solid #d9d9d9',
                  cursor: 'crosshair'
                }}
                onClick={(e) => {
                  const rect = canvasRef.current?.getBoundingClientRect();
                  if (!rect) return;
                  
                  const x = e.clientX - rect.left;
                  const y = e.clientY - rect.top;
                  const centerX = rect.width / 2;
                  const centerY = rect.height / 2;
                  
                  // 检查是否点击了音频源
                  for (const source of audioSources) {
                    const sourceX = centerX + source.position.x * scale;
                    const sourceY = centerY - (viewMode === 'top' ? source.position.z : source.position.y) * scale;
                    const distance = Math.sqrt(Math.pow(x - sourceX, 2) + Math.pow(y - sourceY, 2));
                    
                    if (distance <= 12) {
                      setSelectedSource(source.id);
                      return;
                    }
                  }
                  
                  setSelectedSource('');
                }}
              />
            </Card>
          </Col>
          
          <Col span={8}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {renderSourceControls()}
              {renderListenerControls()}
              {renderSettings()}
            </Space>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default SpatialAudioVisualization;
