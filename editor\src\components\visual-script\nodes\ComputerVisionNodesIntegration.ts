/**
 * 计算机视觉节点编辑器集成
 * 为计算机视觉节点提供专用的编辑器界面和可视化组件
 */

import { NodeEditor } from '../NodeEditor';
import { NodeInfo } from '../../../types/NodeTypes';

export class ComputerVisionNodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Set<string> = new Set();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
  }

  /**
   * 集成所有计算机视觉节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成计算机视觉节点到编辑器...');

    // 集成基础检测节点
    this.integrateDetectionNodes();

    // 集成图像处理节点
    this.integrateImageProcessingNodes();

    // 集成高级视觉节点
    this.integrateAdvancedVisionNodes();

    // 集成生成和增强节点
    this.integrateGenerativeNodes();

    // 集成3D视觉节点
    this.integrate3DVisionNodes();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('计算机视觉节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成基础检测节点
   */
  private integrateDetectionNodes(): void {
    // 物体检测节点
    this.registerNode({
      type: 'ObjectDetection',
      name: '物体检测',
      description: '检测图像中的物体并返回边界框和标签',
      category: 'ComputerVision/Detection',
      icon: 'search',
      color: '#2196F3',
      tags: ['cv', 'detection', 'object'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showBoundingBoxes: true,
        showConfidenceScores: true,
        customPanels: ['detectionResults', 'modelSelector', 'confidenceSettings', 'visualizationOptions']
      }
    });

    // 人脸识别节点
    this.registerNode({
      type: 'FaceRecognition',
      name: '人脸识别',
      description: '检测和识别图像中的人脸',
      category: 'ComputerVision/Detection',
      icon: 'face',
      color: '#2196F3',
      tags: ['cv', 'face', 'recognition'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showFaceLandmarks: true,
        showIdentityResults: true,
        customPanels: ['faceResults', 'landmarkViewer', 'identityDatabase', 'recognitionSettings']
      }
    });

    // 特征提取节点
    this.registerNode({
      type: 'FeatureExtraction',
      name: '特征提取',
      description: '提取图像特征点和描述符',
      category: 'ComputerVision/Detection',
      icon: 'scatter_plot',
      color: '#2196F3',
      tags: ['cv', 'feature', 'extraction'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showFeaturePoints: true,
        showDescriptors: true,
        customPanels: ['featureViewer', 'algorithmSelector', 'featureStatistics', 'matchingResults']
      }
    });

    console.log('基础检测节点集成完成');
  }

  /**
   * 集成图像处理节点
   */
  private integrateImageProcessingNodes(): void {
    // 图像分类节点
    this.registerNode({
      type: 'ImageClassification',
      name: '图像分类',
      description: '对图像进行分类识别',
      category: 'ComputerVision/Processing',
      icon: 'category',
      color: '#2196F3',
      tags: ['cv', 'classification', 'image'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showClassificationResults: true,
        showProbabilities: true,
        customPanels: ['classificationResults', 'modelSelector', 'categoryViewer', 'confidenceChart']
      }
    });

    // 图像分割节点
    this.registerNode({
      type: 'ImageSegmentation',
      name: '图像分割',
      description: '对图像进行语义分割或实例分割',
      category: 'ComputerVision/Processing',
      icon: 'content_cut',
      color: '#2196F3',
      tags: ['cv', 'segmentation', 'semantic'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showSegmentationMask: true,
        showSegmentLabels: true,
        customPanels: ['segmentationResults', 'maskViewer', 'segmentList', 'colorMapping']
      }
    });

    // 图像滤波节点
    this.registerNode({
      type: 'ImageFilter',
      name: '图像滤波',
      description: '对图像应用各种滤波算法',
      category: 'ComputerVision/Processing',
      icon: 'filter',
      color: '#2196F3',
      tags: ['cv', 'filter', 'processing'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showBeforeAfter: true,
        showFilterSettings: true,
        customPanels: ['filterPreview', 'filterSelector', 'parameterTuning', 'histogramAnalysis']
      }
    });

    // 边缘检测节点
    this.registerNode({
      type: 'EdgeDetection',
      name: '边缘检测',
      description: '检测图像中的边缘特征',
      category: 'ComputerVision/Processing',
      icon: 'border_outer',
      color: '#2196F3',
      tags: ['cv', 'edge', 'detection'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showEdgeOverlay: true,
        showThresholdSettings: true,
        customPanels: ['edgeViewer', 'algorithmSelector', 'thresholdTuning', 'edgeStatistics']
      }
    });

    // 光学字符识别节点
    this.registerNode({
      type: 'OpticalCharacterRecognition',
      name: '光学字符识别',
      description: '从图像中识别和提取文本',
      category: 'ComputerVision/Processing',
      icon: 'text_fields',
      color: '#2196F3',
      tags: ['cv', 'ocr', 'text'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showTextOverlay: true,
        showTextResults: true,
        customPanels: ['textResults', 'textViewer', 'languageSelector', 'ocrSettings']
      }
    });

    console.log('图像处理节点集成完成');
  }

  /**
   * 集成高级视觉节点
   */
  private integrateAdvancedVisionNodes(): void {
    // 目标跟踪节点
    this.registerNode({
      type: 'ObjectTracking',
      name: '目标跟踪',
      description: '在视频序列中跟踪目标对象',
      category: 'ComputerVision/Advanced',
      icon: 'track_changes',
      color: '#2196F3',
      tags: ['cv', 'tracking', 'video'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showTrackingTrails: true,
        showTrackingStatus: true,
        customPanels: ['trackingViewer', 'trackerSelector', 'trackingHistory', 'performanceMetrics']
      }
    });

    // 运动追踪节点
    this.registerNode({
      type: 'MotionTracking',
      name: '运动追踪',
      description: '追踪视频序列中的运动',
      category: 'ComputerVision/Advanced',
      icon: 'directions_run',
      color: '#2196F3',
      tags: ['cv', 'motion', 'tracking'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showMotionVectors: true,
        showMotionAnalysis: true,
        customPanels: ['motionViewer', 'vectorDisplay', 'motionAnalysis', 'sensitivitySettings']
      }
    });

    // 光流检测节点
    this.registerNode({
      type: 'OpticalFlow',
      name: '光流检测',
      description: '检测图像序列中的光流',
      category: 'ComputerVision/Advanced',
      icon: 'waves',
      color: '#2196F3',
      tags: ['cv', 'optical', 'flow'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showFlowField: true,
        showFlowAnalysis: true,
        customPanels: ['flowViewer', 'flowField', 'flowAnalysis', 'algorithmSettings']
      }
    });

    // 图像配准节点
    this.registerNode({
      type: 'ImageRegistration',
      name: '图像配准',
      description: '对齐和配准多张图像',
      category: 'ComputerVision/Advanced',
      icon: 'compare',
      color: '#2196F3',
      tags: ['cv', 'registration', 'alignment'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showRegistrationResult: true,
        showTransformMatrix: true,
        customPanels: ['registrationViewer', 'transformDisplay', 'alignmentMetrics', 'methodSelector']
      }
    });

    console.log('高级视觉节点集成完成');
  }

  /**
   * 集成生成和增强节点
   */
  private integrateGenerativeNodes(): void {
    // 图像生成节点
    this.registerNode({
      type: 'ImageGeneration',
      name: '图像生成',
      description: '使用AI生成图像',
      category: 'ComputerVision/Generative',
      icon: 'auto_awesome',
      color: '#2196F3',
      tags: ['cv', 'generation', 'ai'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showGenerationProgress: true,
        showPromptEditor: true,
        customPanels: ['generationViewer', 'promptEditor', 'styleSelector', 'generationSettings']
      }
    });

    // 风格迁移节点
    this.registerNode({
      type: 'StyleTransfer',
      name: '风格迁移',
      description: '将艺术风格应用到图像上',
      category: 'ComputerVision/Generative',
      icon: 'palette',
      color: '#2196F3',
      tags: ['cv', 'style', 'transfer'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showStyleComparison: true,
        showTransferProgress: true,
        customPanels: ['styleViewer', 'styleLibrary', 'transferSettings', 'resultComparison']
      }
    });

    // 图像增强节点
    this.registerNode({
      type: 'ImageEnhancement',
      name: '图像增强',
      description: '增强图像质量和细节',
      category: 'ComputerVision/Generative',
      icon: 'auto_fix_high',
      color: '#2196F3',
      tags: ['cv', 'enhancement', 'quality'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showEnhancementComparison: true,
        showQualityMetrics: true,
        customPanels: ['enhancementViewer', 'qualityAnalysis', 'enhancementSettings', 'beforeAfterComparison']
      }
    });

    console.log('生成和增强节点集成完成');
  }

  /**
   * 集成3D视觉节点
   */
  private integrate3DVisionNodes(): void {
    // 立体视觉节点
    this.registerNode({
      type: 'StereoVision',
      name: '立体视觉',
      description: '基于双目图像计算深度信息',
      category: 'ComputerVision/3D',
      icon: 'view_in_ar',
      color: '#2196F3',
      tags: ['cv', 'stereo', 'depth'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showDepthMap: true,
        showPointCloud: true,
        customPanels: ['stereoViewer', 'depthMapViewer', 'pointCloudViewer', 'calibrationSettings']
      }
    });

    // 深度估计节点
    this.registerNode({
      type: 'DepthEstimation',
      name: '深度估计',
      description: '从单张图像估计深度信息',
      category: 'ComputerVision/3D',
      icon: 'layers',
      color: '#2196F3',
      tags: ['cv', 'depth', 'estimation'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showDepthVisualization: true,
        showDepthStatistics: true,
        customPanels: ['depthViewer', 'depthAnalysis', 'modelSelector', 'depthSettings']
      }
    });

    // 增强现实节点
    this.registerNode({
      type: 'AugmentedReality',
      name: '增强现实',
      description: '在现实场景中叠加虚拟内容',
      category: 'ComputerVision/3D',
      icon: 'view_in_ar',
      color: '#2196F3',
      tags: ['cv', 'ar', 'augmented'],
      nodeClass: null,
      editorConfig: {
        showPreview: true,
        showAROverlay: true,
        showTrackingMarkers: true,
        customPanels: ['arViewer', 'markerTracker', 'virtualObjects', 'arSettings']
      }
    });

    console.log('3D视觉节点集成完成');
  }

  /**
   * 注册节点到编辑器
   */
  private registerNode(nodeInfo: NodeInfo & { editorConfig?: any }): void {
    // 注册到节点编辑器
    this.nodeEditor.registerNode(nodeInfo);

    // 添加到已注册节点集合
    this.registeredNodes.add(nodeInfo.type);

    // 添加到分类映射
    const category = nodeInfo.category;
    if (!this.categoryNodes.has(category)) {
      this.categoryNodes.set(category, []);
    }
    this.categoryNodes.get(category)!.push(nodeInfo.type);

    // 如果有编辑器配置，设置自定义面板
    if (nodeInfo.editorConfig) {
      this.setupCustomPanels(nodeInfo.type, nodeInfo.editorConfig);
    }
  }

  /**
   * 设置自定义面板
   */
  private setupCustomPanels(nodeType: string, editorConfig: any): void {
    if (editorConfig.customPanels) {
      editorConfig.customPanels.forEach((panelType: string) => {
        this.nodeEditor.addCustomPanel(nodeType, panelType, this.createPanelConfig(panelType));
      });
    }
  }

  /**
   * 创建面板配置
   */
  private createPanelConfig(panelType: string): any {
    const panelConfigs: { [key: string]: any } = {
      detectionResults: { title: '检测结果', icon: 'search', resizable: true, collapsible: true },
      modelSelector: { title: '模型选择', icon: 'model_training', resizable: true, collapsible: true },
      confidenceSettings: { title: '置信度设置', icon: 'tune', resizable: true, collapsible: true },
      visualizationOptions: { title: '可视化选项', icon: 'visibility', resizable: true, collapsible: true },
      faceResults: { title: '人脸结果', icon: 'face', resizable: true, collapsible: true },
      landmarkViewer: { title: '关键点查看器', icon: 'place', resizable: true, collapsible: true },
      identityDatabase: { title: '身份数据库', icon: 'people', resizable: true, collapsible: true },
      recognitionSettings: { title: '识别设置', icon: 'settings', resizable: true, collapsible: true },
      featureViewer: { title: '特征查看器', icon: 'scatter_plot', resizable: true, collapsible: false },
      algorithmSelector: { title: '算法选择', icon: 'functions', resizable: false, collapsible: true },
      featureStatistics: { title: '特征统计', icon: 'analytics', resizable: true, collapsible: true },
      matchingResults: { title: '匹配结果', icon: 'compare', resizable: true, collapsible: true },
      classificationResults: { title: '分类结果', icon: 'category', resizable: true, collapsible: true },
      categoryViewer: { title: '类别查看器', icon: 'list', resizable: true, collapsible: true },
      confidenceChart: { title: '置信度图表', icon: 'bar_chart', resizable: true, collapsible: true },
      segmentationResults: { title: '分割结果', icon: 'content_cut', resizable: true, collapsible: true },
      maskViewer: { title: '掩码查看器', icon: 'layers', resizable: true, collapsible: false },
      segmentList: { title: '分割列表', icon: 'list', resizable: true, collapsible: true },
      colorMapping: { title: '颜色映射', icon: 'palette', resizable: true, collapsible: true }
    };

    return panelConfigs[panelType] || {
      title: panelType,
      icon: 'extension',
      resizable: true,
      collapsible: true
    };
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    const cvPalette = {
      category: '计算机视觉',
      nodes: Array.from(this.registeredNodes)
    };

    this.nodeEditor.addNodePalette(cvPalette);
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    this.categoryNodes.forEach((nodes, category) => {
      this.nodeEditor.addNodeCategory({
        name: category,
        displayName: this.getCategoryDisplayName(category),
        icon: this.getCategoryIcon(category),
        color: '#2196F3',
        nodes: nodes
      });
    });
  }

  /**
   * 获取分类显示名称
   */
  private getCategoryDisplayName(category: string): string {
    const displayNames: { [key: string]: string } = {
      'ComputerVision/Detection': '视觉检测',
      'ComputerVision/Processing': '图像处理',
      'ComputerVision/Advanced': '高级视觉',
      'ComputerVision/Generative': '生成增强',
      'ComputerVision/3D': '3D视觉'
    };

    return displayNames[category] || category;
  }

  /**
   * 获取分类图标
   */
  private getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'ComputerVision/Detection': 'search',
      'ComputerVision/Processing': 'image',
      'ComputerVision/Advanced': 'auto_awesome',
      'ComputerVision/Generative': 'brush',
      'ComputerVision/3D': 'view_in_ar'
    };

    return icons[category] || 'visibility';
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): any {
    return {
      totalNodes: this.registeredNodes.size,
      nodesByCategory: Object.fromEntries(this.categoryNodes),
      categories: Array.from(this.categoryNodes.keys())
    };
  }
}
