/**
 * 测试粒子系统节点注册和集成
 */

const fs = require('fs');
const path = require('path');

class ParticleSystemNodesTest {
  constructor() {
    this.results = {
      registration: {
        success: false,
        errors: [],
        warnings: [],
        registeredNodes: []
      },
      integration: {
        success: false,
        errors: [],
        warnings: [],
        integratedNodes: []
      },
      functionality: {
        success: false,
        errors: [],
        warnings: [],
        testedNodes: []
      }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始测试粒子系统节点...\n');

    try {
      // 测试节点注册
      await this.testNodeRegistration();
      
      // 测试编辑器集成
      await this.testEditorIntegration();
      
      // 测试节点功能
      await this.testNodeFunctionality();
      
      // 生成测试报告
      this.generateTestReport();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      this.results.registration.errors.push(`测试执行错误: ${error.message}`);
    }
  }

  /**
   * 测试节点注册
   */
  async testNodeRegistration() {
    console.log('📝 测试粒子系统节点注册...');
    
    try {
      // 检查节点文件是否存在
      const nodeFiles = [
        'engine/src/visual-script/nodes/particles/ParticleEditingNodes.ts',
        'engine/src/visual-script/nodes/particles/ParticleEditingNodes2.ts',
        'engine/src/visual-script/nodes/particles/ParticleForceNodes.ts'
      ];
      
      for (const filePath of nodeFiles) {
        const fullPath = path.resolve(filePath);
        if (!fs.existsSync(fullPath)) {
          this.results.registration.errors.push(`节点文件不存在: ${filePath}`);
          continue;
        }
        
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 检查节点类是否正确导出
        const nodeClasses = [
          'ParticleSystemEditorNode',
          'ParticleEmitterEditorNode',
          'ParticlePreviewNode',
          'ParticleLibraryNode',
          'ParticleExportNode',
          'ParticleImportNode',
          'ParticleForceEditorNode',
          'ParticleCollisionEditorNode'
        ];
        
        nodeClasses.forEach(nodeClass => {
          if (content.includes(`export class ${nodeClass}`)) {
            this.results.registration.registeredNodes.push(nodeClass);
          }
        });
      }
      
      // 检查主NodeRegistry中的注册
      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      if (!fs.existsSync(registryPath)) {
        this.results.registration.errors.push('主NodeRegistry文件不存在');
        return;
      }
      
      const registryContent = fs.readFileSync(registryPath, 'utf8');
      
      // 检查注册函数
      if (!registryContent.includes('registerParticleSystemNodes')) {
        this.results.registration.errors.push('主NodeRegistry中缺少registerParticleSystemNodes函数');
      }
      
      // 检查节点导入
      const requiredImports = [
        'ParticleSystemEditorNode',
        'ParticleEmitterEditorNode',
        'ParticlePreviewNode',
        'ParticleLibraryNode',
        'ParticleForceEditorNode',
        'ParticleCollisionEditorNode'
      ];
      
      requiredImports.forEach(importName => {
        if (!registryContent.includes(importName)) {
          this.results.registration.warnings.push(`主NodeRegistry中缺少导入: ${importName}`);
        }
      });
      
      // 检查节点注册调用
      const expectedRegistrations = [
        'ParticleSystemEditor',
        'ParticleEmitterEditor',
        'ParticlePreview',
        'ParticleLibrary',
        'ParticleExport',
        'ParticleImport',
        'ParticleForceEditor',
        'ParticleCollisionEditor'
      ];
      
      expectedRegistrations.forEach(nodeType => {
        if (registryContent.includes(`'${nodeType}'`)) {
          this.results.registration.registeredNodes.push(nodeType);
        } else {
          this.results.registration.errors.push(`节点未注册: ${nodeType}`);
        }
      });
      
      this.results.registration.success = this.results.registration.errors.length === 0;
      
      console.log(`✅ 节点注册测试完成 - 成功: ${this.results.registration.success}`);
      console.log(`   已注册节点: ${this.results.registration.registeredNodes.length}个`);
      
    } catch (error) {
      this.results.registration.errors.push(`节点注册测试失败: ${error.message}`);
      console.log(`❌ 节点注册测试失败: ${error.message}`);
    }
  }

  /**
   * 测试编辑器集成
   */
  async testEditorIntegration() {
    console.log('🎨 测试编辑器集成...');
    
    try {
      // 检查编辑器集成文件
      const integrationPath = path.resolve('editor/src/components/visual-script/nodes/ParticleSystemNodesIntegration.ts');
      if (!fs.existsSync(integrationPath)) {
        this.results.integration.errors.push('粒子系统节点编辑器集成文件不存在');
        return;
      }
      
      const integrationContent = fs.readFileSync(integrationPath, 'utf8');
      
      // 检查集成类
      if (!integrationContent.includes('class ParticleSystemNodesIntegration')) {
        this.results.integration.errors.push('缺少ParticleSystemNodesIntegration类');
      }
      
      // 检查集成方法
      const requiredMethods = [
        'integrateAllNodes',
        'integrateParticleSystemEditorNode',
        'integrateParticleEmitterEditorNode',
        'integrateParticlePreviewNode',
        'integrateParticleLibraryNode',
        'integrateParticleImportExportNodes',
        'integrateParticleForceEditorNode',
        'integrateParticleCollisionEditorNode'
      ];
      
      requiredMethods.forEach(method => {
        if (integrationContent.includes(method)) {
          this.results.integration.integratedNodes.push(method);
        } else {
          this.results.integration.errors.push(`缺少集成方法: ${method}`);
        }
      });
      
      // 检查自定义面板配置
      const requiredPanels = [
        'particleSystemProperties',
        'emitterList',
        'previewPanel',
        'forceFieldList',
        'colliderList'
      ];
      
      requiredPanels.forEach(panel => {
        if (!integrationContent.includes(panel)) {
          this.results.integration.warnings.push(`缺少自定义面板配置: ${panel}`);
        }
      });
      
      this.results.integration.success = this.results.integration.errors.length === 0;
      
      console.log(`✅ 编辑器集成测试完成 - 成功: ${this.results.integration.success}`);
      console.log(`   集成方法: ${this.results.integration.integratedNodes.length}个`);
      
    } catch (error) {
      this.results.integration.errors.push(`编辑器集成测试失败: ${error.message}`);
      console.log(`❌ 编辑器集成测试失败: ${error.message}`);
    }
  }

  /**
   * 测试节点功能
   */
  async testNodeFunctionality() {
    console.log('⚙️ 测试节点功能...');
    
    try {
      // 检查节点端口配置
      const nodeFiles = [
        'engine/src/visual-script/nodes/particles/ParticleEditingNodes.ts',
        'engine/src/visual-script/nodes/particles/ParticleEditingNodes2.ts',
        'engine/src/visual-script/nodes/particles/ParticleForceNodes.ts'
      ];
      
      for (const filePath of nodeFiles) {
        const fullPath = path.resolve(filePath);
        if (!fs.existsSync(fullPath)) continue;
        
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 检查端口设置方法
        if (content.includes('setupPorts')) {
          this.results.functionality.testedNodes.push(`${path.basename(filePath)} - setupPorts`);
        }
        
        // 检查执行方法
        if (content.includes('execute')) {
          this.results.functionality.testedNodes.push(`${path.basename(filePath)} - execute`);
        }
        
        // 检查输入输出端口
        if (content.includes('addInput') && content.includes('addOutput')) {
          this.results.functionality.testedNodes.push(`${path.basename(filePath)} - ports`);
        }
      }
      
      this.results.functionality.success = this.results.functionality.testedNodes.length > 0;
      
      console.log(`✅ 节点功能测试完成 - 成功: ${this.results.functionality.success}`);
      console.log(`   测试项目: ${this.results.functionality.testedNodes.length}个`);
      
    } catch (error) {
      this.results.functionality.errors.push(`节点功能测试失败: ${error.message}`);
      console.log(`❌ 节点功能测试失败: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    console.log('\n📊 粒子系统节点测试报告');
    console.log('='.repeat(50));
    
    // 注册测试结果
    console.log('\n📝 节点注册测试:');
    console.log(`   状态: ${this.results.registration.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   已注册节点: ${this.results.registration.registeredNodes.length}个`);
    if (this.results.registration.errors.length > 0) {
      console.log('   错误:');
      this.results.registration.errors.forEach(error => console.log(`     - ${error}`));
    }
    if (this.results.registration.warnings.length > 0) {
      console.log('   警告:');
      this.results.registration.warnings.forEach(warning => console.log(`     - ${warning}`));
    }
    
    // 集成测试结果
    console.log('\n🎨 编辑器集成测试:');
    console.log(`   状态: ${this.results.integration.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   集成方法: ${this.results.integration.integratedNodes.length}个`);
    if (this.results.integration.errors.length > 0) {
      console.log('   错误:');
      this.results.integration.errors.forEach(error => console.log(`     - ${error}`));
    }
    if (this.results.integration.warnings.length > 0) {
      console.log('   警告:');
      this.results.integration.warnings.forEach(warning => console.log(`     - ${warning}`));
    }
    
    // 功能测试结果
    console.log('\n⚙️ 节点功能测试:');
    console.log(`   状态: ${this.results.functionality.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`   测试项目: ${this.results.functionality.testedNodes.length}个`);
    if (this.results.functionality.errors.length > 0) {
      console.log('   错误:');
      this.results.functionality.errors.forEach(error => console.log(`     - ${error}`));
    }
    
    // 总体结果
    const overallSuccess = this.results.registration.success && 
                          this.results.integration.success && 
                          this.results.functionality.success;
    
    console.log('\n🎯 总体结果:');
    console.log(`   状态: ${overallSuccess ? '✅ 全部成功' : '❌ 存在问题'}`);
    console.log(`   粒子系统节点集成: ${overallSuccess ? '完成' : '需要修复'}`);
    
    console.log('\n' + '='.repeat(50));
  }
}

// 运行测试
if (require.main === module) {
  const test = new ParticleSystemNodesTest();
  test.runAllTests().catch(console.error);
}

module.exports = ParticleSystemNodesTest;
