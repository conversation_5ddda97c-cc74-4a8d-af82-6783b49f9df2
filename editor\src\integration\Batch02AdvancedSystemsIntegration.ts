/**
 * 批次0.2高级系统节点编辑器集成管理器
 * 负责管理68个高级系统节点的编辑器集成过程
 */

import { NodeEditor } from '../components/visual-script/NodeEditor';
import { Batch02AdvancedSystemsIntegration } from '../components/visual-script/nodes/Batch02AdvancedSystemsIntegration';
import { batch02NodesRegistry } from '../libs/dl-engine';

/**
 * 集成状态接口
 */
interface IntegrationStatus {
  completed: boolean;
  totalNodes: number;
  integratedNodes: number;
  categories: string[];
  errors: string[];
  startTime?: number;
  endTime?: number;
}

/**
 * 批次0.2高级系统节点集成管理器
 */
export class Batch02AdvancedSystemsIntegrationManager {
  private nodeEditor: NodeEditor;
  private integration: Batch02AdvancedSystemsIntegration | null = null;
  private status: IntegrationStatus;

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.status = {
      completed: false,
      totalNodes: 68,
      integratedNodes: 0,
      categories: [],
      errors: []
    };
  }

  /**
   * 初始化并执行高级系统节点集成
   */
  public async initializeAdvancedSystemsNodes(): Promise<boolean> {
    try {
      console.log('🚀 开始批次0.2高级系统节点集成...');
      this.status.startTime = Date.now();

      // 步骤1: 注册节点到引擎
      await this.registerNodesToEngine();

      // 步骤2: 创建编辑器集成
      await this.createEditorIntegration();

      // 步骤3: 集成节点到编辑器
      await this.integrateNodesToEditor();

      // 步骤4: 验证集成结果
      await this.validateIntegration();

      this.status.completed = true;
      this.status.endTime = Date.now();

      console.log('✅ 批次0.2高级系统节点集成完成');
      console.log(`📊 集成统计: ${this.status.integratedNodes}/${this.status.totalNodes} 个节点`);
      console.log(`⏱️ 耗时: ${this.status.endTime - this.status.startTime!}ms`);

      return true;
    } catch (error) {
      console.error('❌ 批次0.2高级系统节点集成失败:', error);
      this.status.errors.push(error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * 注册节点到引擎
   */
  private async registerNodesToEngine(): Promise<void> {
    console.log('📝 注册高级系统节点到引擎...');
    
    // 确保批次0.2节点注册表已初始化
    if (!batch02NodesRegistry.isRegistered()) {
      batch02NodesRegistry.registerAllNodes();
    }

    // 验证注册状态
    const stats = batch02NodesRegistry.getBatch02Statistics();
    console.log('📊 引擎节点注册统计:', stats);
  }

  /**
   * 创建编辑器集成
   */
  private async createEditorIntegration(): Promise<void> {
    console.log('🔧 创建编辑器集成实例...');
    
    this.integration = new Batch02AdvancedSystemsIntegration(this.nodeEditor);
    
    if (!this.integration) {
      throw new Error('无法创建编辑器集成实例');
    }
  }

  /**
   * 集成节点到编辑器
   */
  private async integrateNodesToEditor(): Promise<void> {
    console.log('🎨 集成节点到编辑器界面...');
    
    if (!this.integration) {
      throw new Error('编辑器集成实例未创建');
    }

    // 执行节点集成
    this.integration.integrateAllNodes();

    // 获取集成统计
    const stats = this.integration.getIntegrationStats();
    this.status.integratedNodes = stats.totalNodes;
    this.status.categories = Object.keys(stats.nodesByCategory);

    console.log('📈 编辑器集成统计:', stats);
  }

  /**
   * 验证集成结果
   */
  private async validateIntegration(): Promise<void> {
    console.log('🔍 验证集成结果...');
    
    if (!this.integration) {
      throw new Error('编辑器集成实例未创建');
    }

    // 验证节点数量
    const registeredNodes = this.integration.getRegisteredNodes();
    if (registeredNodes.size !== this.status.totalNodes) {
      throw new Error(`节点数量不匹配: 期望 ${this.status.totalNodes}, 实际 ${registeredNodes.size}`);
    }

    // 验证分类数量
    const categories = this.integration.getNodeCategories();
    if (categories.size < 9) { // 应该有9个分类
      throw new Error(`分类数量不足: 期望至少 9, 实际 ${categories.size}`);
    }

    // 验证关键节点
    const keyNodes = [
      'MultiTouchNode', 'GestureRecognitionNode', 'AnimationStateMachineNode',
      'IKSystemNode', 'AudioMixerNode', 'SpatialAudioNode', 'SoftBodyPhysicsNode',
      'FluidSimulationNode'
    ];

    for (const nodeType of keyNodes) {
      if (!this.integration.isNodeRegistered(nodeType)) {
        throw new Error(`关键节点未注册: ${nodeType}`);
      }
    }

    console.log('✅ 集成验证通过');
  }

  /**
   * 获取集成状态
   */
  public getIntegrationStatus(): IntegrationStatus {
    return { ...this.status };
  }

  /**
   * 获取已注册的节点列表
   */
  public getRegisteredNodes(): string[] {
    if (!this.integration) {
      return [];
    }
    return Array.from(this.integration.getRegisteredNodes().keys());
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): { [category: string]: string[] } {
    if (!this.integration) {
      return {};
    }
    
    const categories = this.integration.getNodeCategories();
    const result: { [category: string]: string[] } = {};
    
    for (const [category, nodes] of categories.entries()) {
      result[category] = nodes;
    }
    
    return result;
  }

  /**
   * 检查特定节点是否已集成
   */
  public isNodeIntegrated(nodeType: string): boolean {
    return this.integration?.isNodeRegistered(nodeType) || false;
  }

  /**
   * 获取节点配置
   */
  public getNodeConfig(nodeType: string): any {
    return this.integration?.getNodeConfig(nodeType);
  }

  /**
   * 重新集成（用于开发和调试）
   */
  public async reintegrate(): Promise<boolean> {
    console.log('🔄 重新执行批次0.2高级系统节点集成...');
    
    // 重置状态
    this.status = {
      completed: false,
      totalNodes: 68,
      integratedNodes: 0,
      categories: [],
      errors: []
    };
    
    this.integration = null;
    
    // 重新执行集成
    return await this.initializeAdvancedSystemsNodes();
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.integration = null;
    this.status = {
      completed: false,
      totalNodes: 68,
      integratedNodes: 0,
      categories: [],
      errors: []
    };
    console.log('🧹 批次0.2高级系统节点集成资源已清理');
  }
}

/**
 * 创建并执行批次0.2高级系统节点集成
 */
export async function createBatch02AdvancedSystemsIntegration(
  nodeEditor: NodeEditor
): Promise<Batch02AdvancedSystemsIntegrationManager> {
  const manager = new Batch02AdvancedSystemsIntegrationManager(nodeEditor);
  await manager.initializeAdvancedSystemsNodes();
  return manager;
}

/**
 * 全局集成管理器实例
 */
let globalIntegrationManager: Batch02AdvancedSystemsIntegrationManager | null = null;

/**
 * 获取全局集成管理器实例
 */
export function getBatch02AdvancedSystemsIntegrationManager(): Batch02AdvancedSystemsIntegrationManager | null {
  return globalIntegrationManager;
}

/**
 * 设置全局集成管理器实例
 */
export function setBatch02AdvancedSystemsIntegrationManager(manager: Batch02AdvancedSystemsIntegrationManager): void {
  globalIntegrationManager = manager;
}
